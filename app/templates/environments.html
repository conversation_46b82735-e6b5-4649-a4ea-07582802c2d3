<div class="container-fluid mt-3">
    <div class="row mb-3">
        <div class="col-md-auto">
            <h4>Selected Environment</h4>
        </div>
        <div class="col-md-3">
            <select class="form-select" id="selectedEnvironment">
                <option selected>Select</option>
                <option value="env1">ENV1</option>
                <option value="env2">ENV2</option>
            </select>
        </div>
        <div class="col-md-auto">
            <button class="btn btn-primary" id="saveEnvironment">Save</button>
        </div>
        <div class="col-md-auto">
            <button class="btn btn-danger" id="deleteEnvironmentBtn"><i class="bi bi-trash"></i> Delete</button>
        </div>
        <div class="col-md-auto">
            <button class="btn btn-info" id="duplicateEnvironmentBtn"><i class="bi bi-files"></i> Duplicate</button>
        </div>
        <div class="col-md-auto">
            <button class="btn btn-success" id="createNewEnvironmentBtn"><i class="bi bi-plus-circle"></i> Create New</button>
        </div>
        <div class="col-md-auto">
            <button class="btn btn-outline-primary" id="importEnvironmentBtn"><i class="bi bi-upload"></i> Import</button>
        </div>
        <div class="col-md-auto">
            <button class="btn btn-outline-secondary" id="exportEnvironmentBtn"><i class="bi bi-download"></i> Export</button>
        </div>
    </div>

    <div class="row">
        <!-- Left column for environment list -->
        <div class="col-md-3">
            <div class="list-group">
                <a href="#" class="list-group-item list-group-item-action active" aria-current="true" data-env="env1">
                    ENV1
                </a>
                <a href="#" class="list-group-item list-group-item-action" data-env="env2">
                    ENV2
                </a>
            </div>
        </div>

        <!-- Right column for environment variables -->
        <div class="col-md-9">
            <div class="d-flex justify-content-between align-items-center mb-2">
                <h5 id="environmentNameDisplay">ENV1</h5>
                <div>
                    <!-- Future icons like Save, Fork, Share can go here -->
                </div>
            </div>
            <div class="input-group mb-3">
                <span class="input-group-text"><i class="bi bi-search"></i></span>
                <input type="text" class="form-control" placeholder="Filter variables" id="filterVariables">
            </div>
            <table class="table table-bordered table-hover">
                <thead>
                    <tr>
                        <th scope="col" style="width: 5%;"><input class="form-check-input" type="checkbox" value="" id="selectAllVariables" checked></th>
                        <th scope="col">Variable</th>
                        <th scope="col">Initial value</th>
                        <th scope="col">Current value</th>
                        <th scope="col" style="width: 5%;"></th> <!-- Actions -->
                    </tr>
                </thead>
                <tbody id="environmentVariablesTableBody">
                    <!-- ENV1 Data -->
                    <tr class="env-vars" data-env="env1">
                        <td><input class="form-check-input" type="checkbox" value="" checked></td>
                        <td>Product-Name</td>
                        <td>Uno card</td>
                        <td>Uno card</td>
                        <td><button class="btn btn-sm btn-link"><i class="bi bi-three-dots"></i></button></td>
                    </tr>
                    <tr class="env-vars" data-env="env1">
                        <td><input class="form-check-input" type="checkbox" value="" checked></td>
                        <td>Item-ID</td>
                        <td>12345</td>
                        <td>12345</td>
                        <td><button class="btn btn-sm btn-link"><i class="bi bi-three-dots"></i></button></td>
                    </tr>
                    <!-- ENV2 Data (initially hidden) -->
                     <tr class="env-vars d-none" data-env="env2">
                        <td><input class="form-check-input" type="checkbox" value="" checked></td>
                        <td>API-Key</td>
                        <td>xyz123abc</td>
                        <td>xyz123abc</td>
                        <td><button class="btn btn-sm btn-link"><i class="bi bi-three-dots"></i></button></td>
                    </tr>
                    <tr class="env-vars d-none" data-env="env2">
                        <td><input class="form-check-input" type="checkbox" value="" checked></td>
                        <td>User-Secret</td>
                        <td>securepass</td>
                        <td>securepass</td>
                        <td><button class="btn btn-sm btn-link"><i class="bi bi-three-dots"></i></button></td>
                    </tr>
                </tbody>
            </table>
            <button class="btn btn-outline-primary" id="addNewVariable"><i class="bi bi-plus-lg"></i> Add new variable</button>
        </div>
    </div>
</div>

<!-- Basic JS for tab-like behavior of environments and filtering (no backend) -->
<script>
document.addEventListener('DOMContentLoaded', function () {
    const environmentLinks = document.querySelectorAll('.list-group-item[data-env]');
    const selectedEnvironmentDropdown = document.getElementById('selectedEnvironment');
    const environmentNameDisplay = document.getElementById('environmentNameDisplay');
    const environmentVariablesTableBody = document.getElementById('environmentVariablesTableBody');
    const filterInput = document.getElementById('filterVariables');
    const selectAllVariablesCheckbox = document.getElementById('selectAllVariables');
    const addNewVariableButton = document.getElementById('addNewVariable');
    const createNewEnvironmentButton = document.getElementById('createNewEnvironmentBtn');
    const deleteEnvironmentBtn = document.getElementById('deleteEnvironmentBtn');
    const duplicateEnvironmentBtn = document.getElementById('duplicateEnvironmentBtn');
    const saveEnvironmentBtn = document.getElementById('saveEnvironment');
    const leftEnvListGroup = document.querySelector('.col-md-3 .list-group'); // For dynamic population

    // Set initial state of buttons
    deleteEnvironmentBtn.disabled = true;
    duplicateEnvironmentBtn.disabled = true;

    let currentSelectedEnvId = null; // Currently selected in UI (temporary until saved)
    let activeEnvironmentId = null; // The actually active/saved environment
    let environmentsData = []; // To store fetched environments
    let currentVariablesData = {}; // Store original data for variable rows being edited {rowHash: data}
    
    console.log('Page loaded, checking for persisted environment...');
    console.log('Session storage active environment:', getActiveEnvironmentFromSession());

    // --- Toast Notification Placeholder ---
    // Assume showToast(message, type = 'success' | 'error' | 'info') is globally available
    // from main.js or similar. e.g. window.showToast('Operation successful!', 'success');
    function showAppToast(message, type = 'info') { // Default to info if not specified
        if (window.showToast) {
            window.showToast(message, type);
        } else {
            console.log(`Toast (${type}): ${message}`);
            alert(`Toast (${type}): ${message}`); // Fallback if showToast is not defined
        }
    }

    // --- API Helper ---
    async function apiCall(url, method = 'GET', body = null) {
        const options = {
            method,
            headers: {
                'Content-Type': 'application/json',
            },
        };
        if (body) {
            options.body = JSON.stringify(body);
        }
        try {
            const response = await fetch(url, options);
            const responseData = await response.json(); 
            if (!response.ok) {
                const errorMsg = responseData.error || `API Error: ${response.status}`;
                showAppToast(errorMsg, 'error');
                console.error('API Error:', response.status, responseData);
                return { success: false, data: responseData, status: response.status }; 
            }
            return { success: true, data: responseData, status: response.status };
        } catch (error) {
            showAppToast('Network or server error: ' + error.message, 'error');
            console.error('Fetch Error:', error);
            return { success: false, error: error.message, data: { error: error.message} };
        }
    }

    // Additional persistence mechanism using sessionStorage
    function saveActiveEnvironmentToSession(envId) {
        if (envId) {
            sessionStorage.setItem('activeEnvironmentId', envId);
            console.log('Saved active environment to session storage:', envId);
        } else {
            sessionStorage.removeItem('activeEnvironmentId');
            console.log('Cleared active environment from session storage');
        }
    }
    
    function getActiveEnvironmentFromSession() {
        const envId = sessionStorage.getItem('activeEnvironmentId');
        return envId ? parseInt(envId) : null;
    }

    // --- Environment Management ---
    async function loadEnvironments() {
        const result = await apiCall('/api/environments');
        if (result.success) {
            environmentsData = result.data;
            
            // Get the currently active environment from the server
            const currentResult = await apiCall('/api/environments/current', 'GET');
            console.log('Current environment response:', currentResult);
            
            let activeEnvFound = false;
            
            if (currentResult.success) {
                // The server returns the environment directly, not wrapped in environment_id
                if (currentResult.data && currentResult.data.id) {
                    activeEnvironmentId = parseInt(currentResult.data.id);
                    console.log('Setting active environment from server:', activeEnvironmentId);
                    activeEnvFound = true;
                }
            }
            
            // If no active environment from server, try session storage
            if (!activeEnvFound) {
                const sessionEnvId = getActiveEnvironmentFromSession();
                if (sessionEnvId && environmentsData.some(env => parseInt(env.id) === sessionEnvId)) {
                    console.log('Setting active environment from session storage:', sessionEnvId);
                    activeEnvironmentId = sessionEnvId;
                    activeEnvFound = true;
                }
            }
            
            // Load the active environment or default to first one
            if (activeEnvFound) {
                await selectEnvironmentUI(activeEnvironmentId, false);
            } else if (environmentsData.length > 0) {
                await selectEnvironmentUI(environmentsData[0].id, false);
            } else {
                environmentNameDisplay.textContent = 'No Environments';
                environmentVariablesTableBody.innerHTML = '<tr><td colspan="5" class="text-center">No environments created yet. Click "Create New" to add one.</td></tr>';
                selectedEnvironmentDropdown.innerHTML = '<option value="">No Environments</option>';
                leftEnvListGroup.innerHTML = '<p class="text-muted p-2">No environments.</p>';
            }

            // After setting up, populate environment selectors
            populateEnvironmentSelectors(environmentsData);
        } else {
            environmentNameDisplay.textContent = 'Error loading environments';
            environmentVariablesTableBody.innerHTML = '<tr><td colspan="5" class="text-center">Error loading environments.</td></tr>';
        }
    }

    function populateEnvironmentSelectors(environments) {
        selectedEnvironmentDropdown.innerHTML = '<option value="">Select Environment</option>';
        leftEnvListGroup.innerHTML = ''; 
        
        console.log('Populating selectors with active environment:', activeEnvironmentId);

        environments.forEach(env => {
            const option = document.createElement('option');
            option.value = env.id;
            option.textContent = env.name;
            if (env.id === activeEnvironmentId) {
                option.selected = true;
            }
            selectedEnvironmentDropdown.appendChild(option);

            const listItem = document.createElement('a');
            listItem.href = '#';
            listItem.classList.add('list-group-item', 'list-group-item-action');
            listItem.dataset.envId = env.id;
            
            // Add active checkmark if this is the active environment
            const isActive = env.id === activeEnvironmentId;
            listItem.innerHTML = isActive ? 
                `<span class="text-success me-2"><i class="bi bi-check-circle-fill"></i></span>${env.name}` : 
                env.name;
                
            if (isActive) {
                listItem.classList.add('active');
            }
            
            listItem.addEventListener('click', async (e) => {
                e.preventDefault();
                await selectEnvironmentUI(parseInt(env.id), false); // Just UI update, don't save to server yet
            });
            leftEnvListGroup.appendChild(listItem);
        });
    }

    selectedEnvironmentDropdown.addEventListener('change', async function() {
        const envId = this.value ? parseInt(this.value) : null;
        if (envId) {
            await selectEnvironmentUI(envId, false); // Just UI update, don't save to server yet
        } else {
            // "Select Environment" was chosen - just clear UI selection
            currentSelectedEnvId = null;
            environmentNameDisplay.textContent = 'Select Environment';
            environmentVariablesTableBody.innerHTML = '<tr><td colspan="5" class="text-center">Select an environment to see its variables.</td></tr>';

            // Clear active selection in the list group but don't update server
            leftEnvListGroup.querySelectorAll('.list-group-item').forEach(link => {
                link.classList.remove('active');
            });

            // Disable action buttons
            deleteEnvironmentBtn.disabled = true;
            duplicateEnvironmentBtn.disabled = true;
        }
    });
    
    // Update saveEnvironmentBtn click handler to also save to session storage
    saveEnvironmentBtn.addEventListener('click', async function() {
        if (!currentSelectedEnvId) {
            showAppToast('Please select an environment first.', 'info');
            return;
        }
        
        // Ensure we're passing an integer ID
        const envId = parseInt(currentSelectedEnvId);
        
        // Set the active environment on the server
        const result = await apiCall('/api/environments/current', 'POST', { environment_id: envId });
        if (result.success) {
            activeEnvironmentId = envId;
            const envName = environmentsData.find(env => parseInt(env.id) === envId)?.name || 'Selected environment';
            showAppToast(`Environment '${envName}' is now active.`, 'success');
            
            // Save to session storage as backup
            saveActiveEnvironmentToSession(envId);
            
            // Update the UI to show the active environment
            refreshEnvironmentIndicators();
        }
    });
    
    // Selects an environment in the UI without necessarily making it active on the server
    async function selectEnvironmentUI(envId, saveToServer = false) {
        // Ensure envId is an integer
        envId = parseInt(envId);
        
        console.log('Selecting environment UI:', envId, 'Current active:', activeEnvironmentId);
        
        currentSelectedEnvId = envId;
        const selectedEnv = environmentsData.find(env => parseInt(env.id) === envId);

        // Enable/disable delete and duplicate buttons based on selection
        deleteEnvironmentBtn.disabled = !envId;
        duplicateEnvironmentBtn.disabled = !envId;

        if (selectedEnv) {
            environmentNameDisplay.textContent = selectedEnv.name;
            selectedEnvironmentDropdown.value = envId; // Ensure dropdown is in sync

            // Update UI to show this environment is selected (but not necessarily active)
            leftEnvListGroup.querySelectorAll('.list-group-item').forEach(link => {
                link.classList.remove('active');
                if (parseInt(link.dataset.envId) === envId) {
                    link.classList.add('active');
                }
            });
            
            await loadEnvironmentVariables(envId);
            
            // If saveToServer is true, also make this the active environment on the server
            if (saveToServer) {
                const setResult = await apiCall('/api/environments/current', 'POST', { environment_id: envId });
                if (setResult.success) {
                    activeEnvironmentId = envId;
                    showAppToast(`Environment '${selectedEnv.name}' is now active.`, 'success');
                    refreshEnvironmentIndicators();
                } else {
                    showAppToast(`Failed to set '${selectedEnv.name}' as active on server.`, 'error');
                }
            }
        } else {
            currentSelectedEnvId = null;
            environmentNameDisplay.textContent = 'Select Environment';
            environmentVariablesTableBody.innerHTML = '<tr><td colspan="5" class="text-center">Select an environment to see its variables.</td></tr>';
            selectedEnvironmentDropdown.value = '';

            leftEnvListGroup.querySelectorAll('.list-group-item').forEach(link => {
                link.classList.remove('active');
            });
        }
        applyFilter();
    }
    
    // Updates the visual indicators for the active environment
    function refreshEnvironmentIndicators() {
        // Update the checkmarks in the environment list
        leftEnvListGroup.querySelectorAll('.list-group-item').forEach(link => {
            const envId = parseInt(link.dataset.envId);
            const isActive = envId === activeEnvironmentId;
            
            // Update the icon
            if (isActive) {
                if (!link.querySelector('.bi-check-circle-fill')) {
                    link.innerHTML = `<span class="text-success me-2"><i class="bi bi-check-circle-fill"></i></span>${link.textContent}`;
                }
            } else {
                const checkIcon = link.querySelector('.bi-check-circle-fill');
                if (checkIcon) {
                    link.textContent = link.textContent.trim(); // Remove the icon and keep only text
                }
            }
        });
    }

    createNewEnvironmentButton.addEventListener('click', async () => {
        const envName = prompt("Enter the name for the new environment:");
        if (envName && envName.trim()) {
            const result = await apiCall('/api/environments', 'POST', { name: envName.trim() });
            if (result.success) {
                showAppToast(`Environment '${envName.trim()}' created.`, 'success');
                await loadEnvironments();
                 if (result.data && result.data.environment && result.data.environment.id) {
                    await selectEnvironmentUI(result.data.environment.id, false);
                }
            }
        }
    });

    // --- Variable Management ---
    async function loadEnvironmentVariables(envId) {
        if (!envId) {
            environmentVariablesTableBody.innerHTML = '<tr><td colspan="5" class="text-center">No environment selected.</td></tr>';
            return;
        }
        const result = await apiCall(`/api/environments/${envId}/variables`);
        environmentVariablesTableBody.innerHTML = '';
        if (result.success && result.data) {
            if (result.data.length === 0) {
                environmentVariablesTableBody.innerHTML = '<tr><td colspan="5" class="text-center">No variables. Click "Add new variable".</td></tr>';
            } else {
                result.data.forEach(variable => renderVariableRow(variable));
            }
        } else {
             environmentVariablesTableBody.innerHTML = '<tr><td colspan="5" class="text-center">Error loading variables.</td></tr>';
        }
        applyFilter();
    }
    
    // Generate a simple hash for a row to track its original data during editing
    function getRowHash(row) {
        return 'row-' + Array.from(row.cells).map(cell => cell.textContent.slice(0,5)).join('-').replace(/\s+/g, '');
    }

    function renderVariableRow(variable, isNew = false) {
        const row = environmentVariablesTableBody.insertRow();
        row.dataset.variableId = variable.id || '';
        row.classList.add('env-vars');
        const rowId = getRowHash(row); // Use for temp storage of original data
        currentVariablesData[rowId] = JSON.parse(JSON.stringify(variable)); // Store a copy

        row.innerHTML = `
            <td><input class="form-check-input" type="checkbox" value="" checked></td>
            <td data-field="name">${variable.name || 'New-Variable'}</td>
            <td data-field="initial_value">${variable.initial_value || ''}</td>
            <td data-field="current_value">${variable.current_value || ''}</td>
            <td>
                <div class="dropdown action-menu">
                    <button class="btn btn-sm btn-link" type="button" data-bs-toggle="dropdown" aria-expanded="false">
                        <i class="bi bi-three-dots"></i>
                    </button>
                    <ul class="dropdown-menu">
                        <li><a class="dropdown-item action-edit-var" href="#">Edit</a></li>
                        <li><a class="dropdown-item action-delete-var text-danger" href="#">Delete</a></li>
                    </ul>
                </div>
                <button class="btn btn-sm btn-success action-save-var d-none">Save</button>
                <button class="btn btn-sm btn-secondary action-cancel-var d-none ms-1">Cancel</button>
            </td>
        `;

        row.querySelector('.action-edit-var').addEventListener('click', (e) => {
            e.preventDefault();
            editVariableRow(row, currentVariablesData[rowId]);
        });
        row.querySelector('.action-delete-var').addEventListener('click', async (e) => {
            e.preventDefault();
            await deleteVariable(variable.id, row);
        });
        row.querySelector('.action-save-var').addEventListener('click', async (e) => {
            e.preventDefault();
            await saveVariableRow(row, variable.id, false);
        });
        row.querySelector('.action-cancel-var').addEventListener('click', (e) => {
            e.preventDefault();
            cancelEditVariableRow(row, currentVariablesData[rowId], isNew);
        });
        
        if (isNew) {
            makeRowEditable(row, currentVariablesData[rowId] || variable); 
            row.querySelector('.action-menu').classList.add('d-none');
            row.querySelector('.action-save-var').classList.remove('d-none');
            row.querySelector('.action-cancel-var').classList.remove('d-none');
            // Special save handler for new unsaved variable
            row.querySelector('.action-save-var').onclick = async (ev) => {
                ev.preventDefault();
                await saveVariableRow(row, null, true); // isNew = true
            };
        }
    }

    function makeRowEditable(row, originalVariableData) {
        const rowId = getRowHash(row);
        currentVariablesData[rowId] = JSON.parse(JSON.stringify(originalVariableData)); // Save snapshot

        row.querySelectorAll('td[data-field]').forEach(cell => {
            const field = cell.dataset.field;
            const currentValue = originalVariableData[field] || (field === 'name' && !originalVariableData.id ? 'New-Variable' :'');

            // Convert all data fields to input elements (no type field in this table)
            cell.innerHTML = `<input type="text" class="form-control form-control-sm" value="${currentValue}">`;
        });
        row.querySelector('.action-menu').classList.add('d-none');
        row.querySelector('.action-save-var').classList.remove('d-none');
        row.querySelector('.action-cancel-var').classList.remove('d-none');
    }

    function cancelEditVariableRow(row, originalData, isNewRow) {
        if (isNewRow) { // If it was a new, unsaved row, just remove it
            row.remove();
            delete currentVariablesData[getRowHash(row)];
            return;
        }

        // Check if row still exists in DOM
        if (!row || !row.parentNode) {
            console.warn('Row no longer exists in DOM, skipping cancel operation');
            return;
        }

        // Restore original data for existing row with null checks
        const nameCell = row.querySelector('td[data-field="name"]');
        if (nameCell) {
            nameCell.textContent = originalData.name;
        }

        const initialValueCell = row.querySelector('td[data-field="initial_value"]');
        if (initialValueCell) {
            initialValueCell.textContent = originalData.initial_value || '';
        }

        const currentValueCell = row.querySelector('td[data-field="current_value"]');
        if (currentValueCell) {
            currentValueCell.textContent = originalData.current_value || '';
        }

        const actionMenu = row.querySelector('.action-menu');
        if (actionMenu) {
            actionMenu.classList.remove('d-none');
        }

        const saveBtn = row.querySelector('.action-save-var');
        if (saveBtn) {
            saveBtn.classList.add('d-none');
        }

        const cancelBtn = row.querySelector('.action-cancel-var');
        if (cancelBtn) {
            cancelBtn.classList.add('d-none');
        }

        delete currentVariablesData[getRowHash(row)];
    }

    function editVariableRow(row, variableData) {
        makeRowEditable(row, variableData);
    }

    async function saveVariableRow(row, varId, isNewVariable = false) {
        // Check if row still exists in DOM
        if (!row || !row.parentNode) {
            console.warn('Row no longer exists in DOM, skipping save operation');
            return;
        }

        const nameInput = row.querySelector('td[data-field="name"] input');
        const initialValueInput = row.querySelector('td[data-field="initial_value"] input');
        const currentValueInput = row.querySelector('td[data-field="current_value"] input');

        // Check if all required inputs exist (no type field in this table)
        if (!nameInput || !initialValueInput || !currentValueInput) {
            console.error('Required input elements not found in row');
            showAppToast('Error: Required input fields not found.', 'error');
            return;
        }

        const payload = {
            name: nameInput.value.trim(),
            type: 'default', // Default type since no type field in UI
            initial_value: initialValueInput.value.trim(),
            current_value: currentValueInput.value.trim(),
        };

        if (!payload.name) {
            showAppToast('Variable name cannot be empty.', 'error');
            return;
        }

        let result;
        if (isNewVariable) {
            if (!currentSelectedEnvId) {
                showAppToast('No environment selected.', 'error');
                return;
            }
            result = await apiCall(`/api/environments/${currentSelectedEnvId}/variables`, 'POST', payload);
        } else {
            if (!varId) {
                 showAppToast('Variable ID missing.', 'error'); return;
            }
            result = await apiCall(`/api/environment_variables/${varId}`, 'PUT', payload);
        }

        if (result.success) {
            showAppToast(`Variable '${payload.name}' ${isNewVariable ? 'added' : 'updated'}.`, 'success');
            await loadEnvironmentVariables(currentSelectedEnvId);
            delete currentVariablesData[getRowHash(row)];
        } else {
            // Error toast shown by apiCall
            // Optionally, keep row editable if save failed: makeRowEditable(row, payload);
            // For now, reloading variables will revert the row or remove if it was new and failed.
            if(isNewVariable && row.parentNode) row.remove(); // If new var add failed, remove the temp row.
            else await loadEnvironmentVariables(currentSelectedEnvId); // Re-load to revert changes on error
        }
    }

    async function deleteVariable(varId, rowElement) {
        if (!varId) { // Should not happen for persisted variables
            showAppToast('Cannot delete unsaved variable.', 'info');
            rowElement.remove();
            return;
        }
        if (confirm('Are you sure you want to delete this variable?')) {
            const result = await apiCall(`/api/environment_variables/${varId}`, 'DELETE');
            if (result.success) {
                showAppToast('Variable deleted.', 'success');
                rowElement.remove();
            } 
        }
    }

    addNewVariableButton.addEventListener('click', function() {
        if (!currentSelectedEnvId) {
            showAppToast('Please select an environment first.', 'info');
            return;
        }
        const newVarData = { name: '', type: 'default', initial_value: '', current_value: '' }; // Empty name for new
        renderVariableRow(newVarData, true); 
        environmentVariablesTableBody.querySelector('td[data-field="name"] input').focus();
    });

    // --- Delete Environment Button Handler ---
    deleteEnvironmentBtn.addEventListener('click', async function() {
        if (!currentSelectedEnvId) {
            showAppToast('Please select an environment to delete.', 'info');
            return;
        }
        
        const selectedEnv = environmentsData.find(env => parseInt(env.id) === parseInt(currentSelectedEnvId));
        if (!selectedEnv) {
            showAppToast('Environment not found.', 'error');
            return;
        }
        
        if (confirm(`Are you sure you want to delete environment "${selectedEnv.name}" and all its variables? This action cannot be undone.`)) {
            const result = await apiCall(`/api/environments/${currentSelectedEnvId}`, 'DELETE');
            if (result.success) {
                showAppToast(`Environment "${selectedEnv.name}" deleted successfully.`, 'success');
                // If we're deleting the active environment, clear it
                if (activeEnvironmentId === parseInt(currentSelectedEnvId)) {
                    await apiCall('/api/environments/current', 'POST', { environment_id: null });
                    activeEnvironmentId = null;
                    saveActiveEnvironmentToSession(null); // Clear from session storage too
                }
                currentSelectedEnvId = null;
                await loadEnvironments();
            }
        }
    });
    
    // --- Duplicate Environment Button Handler ---
    duplicateEnvironmentBtn.addEventListener('click', async function() {
        if (!currentSelectedEnvId) {
            showAppToast('Please select an environment to duplicate.', 'info');
            return;
        }
        
        const selectedEnv = environmentsData.find(env => env.id === currentSelectedEnvId);
        if (!selectedEnv) {
            showAppToast('Environment not found.', 'error');
            return;
        }
        
        const newName = prompt(`Enter a name for the duplicated environment:`, `${selectedEnv.name} (Copy)`);
        if (!newName || !newName.trim()) {
            return; // User cancelled or provided empty name
        }
        
        // 1. Create new environment
        const createResult = await apiCall('/api/environments', 'POST', { name: newName.trim() });
        if (!createResult.success) {
            return; // Error message shown by apiCall
        }
        
        const newEnvId = createResult.data.environment.id;
        
        // 2. Get variables from current environment
        const varsResult = await apiCall(`/api/environments/${currentSelectedEnvId}/variables`);
        if (!varsResult.success) {
            showAppToast('Created environment but failed to copy variables.', 'error');
            await loadEnvironments();
            return;
        }
        
        // 3. Copy each variable to the new environment
        let copiedCount = 0;
        for (const variable of varsResult.data) {
            const copyResult = await apiCall(`/api/environments/${newEnvId}/variables`, 'POST', {
                name: variable.name,
                type: variable.type,
                initial_value: variable.initial_value,
                current_value: variable.current_value
            });
            
            if (copyResult.success) {
                copiedCount++;
            }
        }
        
        // 4. Reload and select the new environment (just UI selection, don't make it active)
        await loadEnvironments();
        await selectEnvironmentUI(newEnvId, false);
        
        showAppToast(`Environment duplicated with ${copiedCount} variables.`, 'success');
    });

    // --- Filtering and Select All (Adapted) ---
    function applyFilter() {
        if (!filterInput) return;
        const filterText = filterInput.value.toLowerCase();
        const rows = environmentVariablesTableBody.querySelectorAll('tr'); // Simpler selector
        let hasVisibleRows = false;
        rows.forEach(row => {
            if(row.querySelector('td[data-field="name"]')) { // Check if it's a variable row
                const variableName = row.querySelector('td[data-field="name"]').textContent.toLowerCase();
                 if (variableName.includes(filterText)) {
                    row.style.display = '';
                    hasVisibleRows = true;
                } else {
                    row.style.display = 'none';
                }
            } else if (row.cells.length === 1 && row.cells[0].colSpan === 6) {
                // This is a message row like "No variables..." or "Error loading..."
                // Hide it if we have actual rows to show, or if filtering and no matches
                row.style.display = hasVisibleRows ? 'none' : ''; 
            }
        });
    }
    if (filterInput) filterInput.addEventListener('keyup', applyFilter);

    if (selectAllVariablesCheckbox) {
        selectAllVariablesCheckbox.addEventListener('change', function() {
            const checkboxes = environmentVariablesTableBody.querySelectorAll('tr:not([style*="display: none"]) .form-check-input');
            checkboxes.forEach(checkbox => {
                checkbox.checked = this.checked;
            });
        });
    }

    // --- Import/Export Functionality ---
    const importEnvironmentBtn = document.getElementById('importEnvironmentBtn');
    const exportEnvironmentBtn = document.getElementById('exportEnvironmentBtn');

    // Export environment functionality
    exportEnvironmentBtn.addEventListener('click', async function() {
        if (!currentSelectedEnvId) {
            showAppToast('Please select an environment to export.', 'info');
            return;
        }

        try {
            const result = await apiCall(`/api/environments/${currentSelectedEnvId}/export`);
            if (result.success) {
                const envData = result.data;
                const dataStr = JSON.stringify(envData, null, 2);
                const dataBlob = new Blob([dataStr], {type: 'application/json'});

                const link = document.createElement('a');
                link.href = URL.createObjectURL(dataBlob);
                link.download = `environment_${envData.name || 'export'}_${new Date().toISOString().split('T')[0]}.json`;
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);

                showAppToast('Environment exported successfully!', 'success');
            }
        } catch (error) {
            showAppToast('Failed to export environment: ' + error.message, 'error');
        }
    });

    // Import environment functionality
    importEnvironmentBtn.addEventListener('click', function() {
        const input = document.createElement('input');
        input.type = 'file';
        input.accept = '.json';
        input.onchange = async function(event) {
            const file = event.target.files[0];
            if (!file) return;

            try {
                const text = await file.text();
                const envData = JSON.parse(text);

                // Validate the imported data structure
                if (!envData.name) {
                    showAppToast('Invalid environment file: missing name field.', 'error');
                    return;
                }

                const result = await apiCall('/api/environments/import', 'POST', envData);
                if (result.success) {
                    showAppToast(`Environment '${envData.name}' imported successfully!`, 'success');
                    await loadEnvironments();
                    if (result.data && result.data.environment && result.data.environment.id) {
                        await selectEnvironmentUI(result.data.environment.id, false);
                    }
                } else {
                    showAppToast('Failed to import environment: ' + (result.data?.error || 'Unknown error'), 'error');
                }
            } catch (error) {
                showAppToast('Failed to parse environment file: ' + error.message, 'error');
            }
        };
        input.click();
    });

    // Initial Load
    loadEnvironments();
});
</script>