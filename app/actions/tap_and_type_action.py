import logging
import time
import requests
import traceback
from selenium.webdriver.common.by import By
from base_action import BaseAction
from app.utils.parameter_utils import substitute_parameters

class TapAndTypeAction(BaseAction):
    """Action to tap at coordinates and type text"""

    def __init__(self, controller=None):
        """Initialize the action with a controller"""
        super().__init__(controller)
        self.logger = logging.getLogger(__name__)

    def execute(self, params):
        """
        Execute the tap and type action

        Args:
            params (dict): Parameters for the action
                - x (int/str): X coordinate to tap (can be string for env vars)
                - y (int/str): Y coordinate to tap (can be string for env vars)
                - text (str): Text to type
                - method (str, optional): Method to use for tapping ('coordinates' or 'element')
                - timeout (int, optional): Timeout in seconds

        Returns:
            dict: Result of the action with status and message
        """
        self.logger.info(f"Executing tap and type action with params: {params}")

        # Extract parameters
        method = params.get('method', 'coordinates')
        timeout = int(params.get('timeout', 10))

        # Get the text and apply parameter substitution
        original_text = params.get('text', '')
        if not original_text:
            return {"status": "error", "message": "No text provided for tap and type action"}

        # Apply parameter substitution
        text = substitute_parameters(original_text)

        # Log if parameter substitution occurred
        if text != original_text:
            self.logger.info(f"Parameter substitution applied: '{original_text}' -> '{text}'")

        if method == 'coordinates':
            # Get coordinates
            try:
                # Handle both string and int types for coordinates
                # This allows environment variables to be used for coordinates
                x = params.get('x', 0)
                y = params.get('y', 0)
                
                # Convert to int if they're strings containing numbers
                if isinstance(x, str) and x.isdigit():
                    x = int(x)
                if isinstance(y, str) and y.isdigit():
                    y = int(y)
                    
                return self._tap_and_type_at_coordinates(x, y, text, timeout)
            except Exception as e:
                self.logger.error(f"Error executing tap and type action with coordinates: {e}")
                return {"status": "error", "message": f"Tap and type action with coordinates failed: {str(e)}"}
        else:
            return {"status": "error", "message": f"Unsupported method: {method}"}

    def _tap_and_type_at_coordinates(self, x, y, text, timeout=10):
        """
        Tap at coordinates and type text

        Args:
            x (int/str): X coordinate (can be string for env vars)
            y (int/str): Y coordinate (can be string for env vars)
            text (str): Text to type
            timeout (int): Timeout in seconds

        Returns:
            dict: Result with status and message
        """
        self.logger.info(f"Tapping at coordinates ({x}, {y}) and typing: '{text}'")

        try:
            # Get the driver
            driver = self.controller.driver if hasattr(self.controller, 'driver') else None
            if not driver:
                return {"status": "error", "message": "No driver available"}

            # Tap at the coordinates
            try:
                self.logger.info(f"Tapping at coordinates ({x}, {y})")
                if hasattr(driver, 'tap'):
                    driver.tap([(int(x), int(y))])
                else:
                    # Try using execute_script with mobile: tap
                    driver.execute_script('mobile: tap', {'x': int(x), 'y': int(y)})

                # Wait longer for the keyboard to appear
                time.sleep(2)  # Increased wait time

                # Tap again to ensure the field is focused
                try:
                    self.logger.info(f"Tapping again at coordinates ({x}, {y}) to ensure focus")
                    if hasattr(driver, 'tap'):
                        driver.tap([(int(x), int(y))])
                    else:
                        # Try using execute_script with mobile: tap
                        driver.execute_script('mobile: tap', {'x': int(x), 'y': int(y)})
                    time.sleep(1)  # Wait after second tap
                except Exception as e:
                    self.logger.warning(f"Second tap failed: {e}")

                # Try different text input methods based on platform
                success = False
                platform_name = driver.capabilities.get('platformName', '').lower()

                # First try the direct keys endpoint method (works well for hidden fields)
                self.logger.info(f"Trying direct keys endpoint method first for text: '{text}'")
                try:
                    # Force use of direct keys endpoint and don't fall back silently
                    direct_keys_result = self._use_direct_keys_endpoint(text)
                    if direct_keys_result:
                        self.logger.info("✅ Direct keys endpoint method succeeded")
                        success = True
                        # Return immediately after success to avoid falling back to other methods
                        return {
                            "status": "success",
                            "message": f"Tap and Type successful at coordinates ({x}, {y}) with text: '{text}' using direct keys endpoint"
                        }
                    else:
                        self.logger.warning("❌ Direct keys endpoint method explicitly failed, trying platform-specific methods")
                except Exception as direct_keys_error:
                    self.logger.error(f"❌ Direct keys endpoint method threw an exception: {direct_keys_error}")
                    self.logger.error(traceback.format_exc())

                    if platform_name == 'ios':
                        # For iOS, try mobile:typeText first
                        try:
                            self.logger.info(f"Using mobile:typeText for iOS text input: '{text}'")
                            driver.execute_script('mobile: typeText', {'text': text})
                            self.logger.info("mobile:typeText succeeded")
                            success = True
                        except Exception as ios_e1:
                            self.logger.warning(f"mobile:typeText failed: {ios_e1}")

                            # Fall back to keyboard simulation as last resort
                            self.logger.info(f"Falling back to keyboard simulation for text: '{text}'")
                            if self._simulate_keyboard_typing(text):
                                self.logger.info("Keyboard simulation succeeded")
                                success = True
                            else:
                                self.logger.error("Keyboard simulation failed")
                                return {
                                    "status": "error",
                                    "message": f"All iOS text input methods failed for text: '{text}'"
                                }
                    else:
                        # For Android, try using ADB shell input text command
                        try:
                            self.logger.info(f"Using ADB shell input text for Android: '{text}'")
                            driver.execute_script('mobile: shell', {
                                'command': 'input',
                                'args': ['text', text]
                            })
                            self.logger.info("ADB input text succeeded")
                            success = True
                        except Exception as e:
                            self.logger.error(f"ADB input text failed: {e}")
                            return {
                                "status": "error",
                                "message": f"Failed to input text on Android: {str(e)}"
                            }

                # If we get here and success is still False, all methods failed
                if not success:
                    self.logger.error("Text input failed")
                    return {
                        "status": "error",
                        "message": f"Text input failed after tapping coordinates ({x}, {y})"
                    }

                # Try to close the keyboard after successful text entry
                try:
                    self.logger.info("Attempting to close the keyboard")

                    # Method 1: Try using the Return key with XPath (most reliable)
                    try:
                        return_button = driver.find_element(By.XPATH, '//XCUIElementTypeKey[@name="Return"]')
                        return_button.click()
                        self.logger.info("Closed keyboard by tapping 'Return' button using XPath")
                        time.sleep(0.5)
                    except Exception as e1:
                        self.logger.warning(f"Failed to find 'Return' button by XPath: {e1}")

                        # Method 2: Try using the Done button with XPath
                        try:
                            done_button = driver.find_element(By.XPATH, '//XCUIElementTypeButton[@name="Done"]')
                            done_button.click()
                            self.logger.info("Closed keyboard by tapping 'Done' button using XPath")
                            time.sleep(0.5)
                        except Exception as e2:
                            self.logger.warning(f"Failed to find 'Done' button by XPath: {e2}")

                            # Method 3: Try tapping outside the keyboard (top of screen)
                            try:
                                window_size = driver.get_window_size()
                                width = window_size['width']
                                # Tap near the top of the screen
                                driver.tap([(width // 2, 50)])
                                self.logger.info("Attempted to close keyboard by tapping top of screen")
                                time.sleep(0.5)
                            except Exception as e3:
                                self.logger.warning(f"Failed to close keyboard by tapping: {e3}")

                except Exception as e:
                    self.logger.warning(f"Error while trying to close keyboard: {e}")

                # Return success result regardless of keyboard closing
                # Include both original and substituted text in the message if parameter substitution occurred
                if text != original_text:
                    return {
                        "status": "success",
                        "message": f"Tap and Type successful at coordinates ({x}, {y}) with text: '{text}' (substituted from '{original_text}')"
                    }
                else:
                    return {
                        "status": "success",
                        "message": f"Tap and Type successful at coordinates ({x}, {y}) with text: '{text}'"
                    }

            except Exception as tap_e:
                self.logger.error(f"Error tapping coordinates: {tap_e}")
                return {
                    "status": "error",
                    "message": f"Failed to tap coordinates ({x}, {y}): {str(tap_e)}"
                }

        except Exception as e:
            self.logger.error(f"Error executing tap and type action with coordinates: {e}")
            return {"status": "error", "message": f"Tap and type action with coordinates failed: {str(e)}"}

    def _use_direct_keys_endpoint(self, text):
        """
        Use the direct keys endpoint to input text (most reliable method for hidden fields)

        Args:
            text (str): The text to input

        Returns:
            bool: True if successful, False otherwise
        """
        self.logger.info(f"🔑 DIRECT KEYS ENDPOINT: Starting direct keys endpoint method for text: '{text}'")

        try:
            # Get the driver and session ID
            driver = self.controller.driver if hasattr(self.controller, 'driver') else None
            if not driver:
                self.logger.error("❌ DIRECT KEYS ENDPOINT: No driver available")
                return False

            session_id = driver.session_id
            if not session_id:
                self.logger.error("❌ DIRECT KEYS ENDPOINT: No session ID available")
                return False

            self.logger.info(f"🔑 DIRECT KEYS ENDPOINT: Using session ID: {session_id}")

            # Determine Appium server URL - use the default URL that worked in the test script
            appium_url = "http://localhost:4723/wd/hub"  # Default Appium URL with /wd/hub

            # Try to get the actual command executor URL from the driver
            try:
                if hasattr(driver, 'command_executor') and hasattr(driver.command_executor, '_url'):
                    appium_url = driver.command_executor._url
                    self.logger.info(f"🔑 DIRECT KEYS ENDPOINT: Got URL from driver: {appium_url}")
            except Exception as url_error:
                self.logger.warning(f"⚠️ DIRECT KEYS ENDPOINT: Could not get command executor URL, using default: {url_error}")

            # Make sure the URL doesn't end with /session
            if appium_url.endswith('/session'):
                appium_url = appium_url[:-8]  # Remove /session

            self.logger.info(f"🔑 DIRECT KEYS ENDPOINT: Using Appium URL: {appium_url}")

            # Make the direct request to the keys endpoint
            endpoint_url = f"{appium_url}/session/{session_id}/keys"
            self.logger.info(f"🔑 DIRECT KEYS ENDPOINT: Sending POST request to: {endpoint_url}")
            self.logger.info(f"🔑 DIRECT KEYS ENDPOINT: Request body: {{'value': ['{text}']}}")

            response = requests.post(
                endpoint_url,
                json={"value": [text]}
            )

            self.logger.info(f"🔑 DIRECT KEYS ENDPOINT: Response status code: {response.status_code}")

            if response.status_code == 200:
                self.logger.info(f"✅ DIRECT KEYS ENDPOINT: Successfully input text: '{text}'")
                return True
            else:
                self.logger.error(f"❌ DIRECT KEYS ENDPOINT: Failed with status code: {response.status_code}")
                self.logger.error(f"❌ DIRECT KEYS ENDPOINT: Response: {response.text}")

                # Try alternative endpoint format without /wd/hub
                if '/wd/hub' in appium_url:
                    alt_appium_url = appium_url.replace('/wd/hub', '')
                    alt_endpoint_url = f"{alt_appium_url}/session/{session_id}/keys"
                    self.logger.info(f"🔄 DIRECT KEYS ENDPOINT: Trying alternative URL: {alt_endpoint_url}")

                    alt_response = requests.post(
                        alt_endpoint_url,
                        json={"value": [text]}
                    )

                    self.logger.info(f"🔄 DIRECT KEYS ENDPOINT: Alternative response status code: {alt_response.status_code}")

                    if alt_response.status_code == 200:
                        self.logger.info(f"✅ DIRECT KEYS ENDPOINT: Successfully input text using alternative URL: '{text}'")
                        return True
                    else:
                        self.logger.error(f"❌ DIRECT KEYS ENDPOINT: Alternative URL also failed: {alt_response.status_code}")
                        self.logger.error(f"❌ DIRECT KEYS ENDPOINT: Alternative response: {alt_response.text}")

                return False

        except Exception as e:
            self.logger.error(f"❌ DIRECT KEYS ENDPOINT: Error: {e}")
            self.logger.error(traceback.format_exc())
            return False

    def _simulate_keyboard_typing(self, text):
        """
        Simulate typing by tapping each character on the iOS keyboard

        Args:
            text (str): The text to type

        Returns:
            bool: True if successful, False otherwise
        """
        self.logger.info(f"Simulating keyboard typing for text: '{text}'")

        if not text:
            self.logger.warning("No text provided for keyboard simulation")
            return False

        driver = self.controller.driver if hasattr(self.controller, 'driver') else None
        if not driver:
            self.logger.warning("No driver available for keyboard simulation")
            return False

        try:
            # Check if we're on iOS
            platform_name = driver.capabilities.get('platformName', '').lower()
            if platform_name != 'ios':
                self.logger.warning(f"Keyboard simulation is only supported on iOS, current platform: {platform_name}")
                return False

            # Wait for keyboard to appear
            self.logger.info("Waiting for keyboard to appear...")
            keyboard_found = False
            max_wait_time = 15  # Maximum time to wait for keyboard in seconds
            start_time = time.time()

            while time.time() - start_time < max_wait_time and not keyboard_found:
                # Try multiple methods to find the keyboard
                # Method 1: By class name
                try:
                    keyboard = driver.find_element(By.CLASS_NAME, 'XCUIElementTypeKeyboard')
                    self.logger.info("Keyboard found by class name")
                    keyboard_found = True
                    break
                except Exception as e:
                    self.logger.debug(f"Keyboard not found by class name: {e}")

                # Method 2: By XPath
                try:
                    keyboard = driver.find_element(By.XPATH, '//XCUIElementTypeKeyboard')
                    self.logger.info("Keyboard found by XPath")
                    keyboard_found = True
                    break
                except Exception as e:
                    self.logger.debug(f"Keyboard not found by XPath: {e}")

                # Method 3: Check page source
                try:
                    page_source = driver.page_source
                    if 'XCUIElementTypeKeyboard' in page_source:
                        self.logger.info("Keyboard found in page source")
                        keyboard_found = True
                        break
                except Exception as e:
                    self.logger.debug(f"Failed to check page source: {e}")

                # Method 4: Look for common keyboard keys
                try:
                    for key_name in ['q', 'w', 'e', 'r', 't', 'space', 'shift', '123']:
                        try:
                            driver.find_element(By.XPATH, f'//XCUIElementTypeKey[@name="{key_name}"]')
                            self.logger.info(f"Keyboard detected by finding key: {key_name}")
                            keyboard_found = True
                            break
                        except Exception:
                            continue

                    if keyboard_found:
                        break
                except Exception as e:
                    self.logger.debug(f"Failed to find common keyboard keys: {e}")

                # If keyboard not found yet, try tapping the input field again
                if not keyboard_found and time.time() - start_time > 5:  # After 5 seconds, try tapping again
                    try:
                        self.logger.info("Keyboard not detected, trying to tap input field again")
                        if hasattr(self, 'locator_type') and hasattr(self, 'locator_value') and self.locator_type and self.locator_value:
                            element = self._find_element(driver, self.locator_type, self.locator_value)
                            element.click()
                            self.logger.info("Tapped input field again to trigger keyboard")
                        elif hasattr(self, 'x') and hasattr(self, 'y') and self.x is not None and self.y is not None:
                            driver.tap([(self.x, self.y)])
                            self.logger.info(f"Tapped at position ({self.x}, {self.y}) again to trigger keyboard")
                    except Exception as e:
                        self.logger.debug(f"Failed to tap input field again: {e}")

                # Wait a bit and try again
                time.sleep(0.5)

            if not keyboard_found:
                self.logger.warning("Keyboard not found after waiting. Will attempt to type anyway, but may fail.")
                # Try one more time to check if any keyboard keys are visible
                try:
                    for key_name in ['q', 'w', 'e', 'r', 't', 'y', 'u', 'i', 'o', 'p', 'a', 's', 'd', 'f', 'g', 'h', 'j', 'k', 'l', 'z', 'x', 'c', 'v', 'b', 'n', 'm', 'space', 'shift', '123']:
                        try:
                            driver.find_element(By.XPATH, f'//XCUIElementTypeKey[@name="{key_name}"]')
                            self.logger.info(f"Found key '{key_name}' in final check, proceeding with typing")
                            keyboard_found = True
                            break
                        except Exception:
                            continue
                except Exception:
                    pass

                if not keyboard_found:
                    self.logger.error("No keyboard keys found. Typing will likely fail.")
                    # We'll still try to type, but it will probably fail

            # Process each character
            for char in text:
                self.logger.info(f"Typing character: '{char}'")

                # Handle special characters
                if char == ' ':
                    # Find and tap the space key using multiple methods
                    space_found = False

                    # Try multiple XPath expressions for space key
                    for xpath in [
                        '//XCUIElementTypeKey[@name="space"]',
                        '//XCUIElementTypeButton[@name="space"]',
                        '//XCUIElementTypeKey[contains(@name, "space")]',
                        '//XCUIElementTypeButton[contains(@name, "space")]',
                        '//XCUIElementTypeKey[@label="space"]'
                    ]:
                        try:
                            space_key = driver.find_element(By.XPATH, xpath)
                            space_key.click()
                            self.logger.info(f"Tapped space key using XPath: {xpath}")
                            space_found = True
                            time.sleep(0.2)
                            break
                        except Exception:
                            continue

                    if space_found:
                        continue

                    # Try by accessibility ID
                    try:
                        space_key = driver.find_element(By.ACCESSIBILITY_ID, 'space')
                        space_key.click()
                        self.logger.info("Tapped space key by accessibility ID")
                        time.sleep(0.2)
                        continue
                    except Exception as e:
                        self.logger.debug(f"Space key not found by accessibility ID: {e}")

                    # Try by name
                    try:
                        space_key = driver.find_element(By.NAME, 'space')
                        space_key.click()
                        self.logger.info("Tapped space key by name")
                        time.sleep(0.2)
                        continue
                    except Exception as e:
                        self.logger.debug(f"Space key not found by name: {e}")

                    # Try multiple positions for space bar
                    self.logger.info("Trying to tap space bar at common positions")
                    try:
                        # Get screen dimensions
                        window_size = driver.get_window_size()
                        width = window_size['width']
                        height = window_size['height']

                        # Common positions for space bar on iOS keyboards
                        positions = [
                            (width // 2, int(height * 0.9)),  # Bottom center (90% down)
                            (width // 2, int(height * 0.85)),  # Slightly higher (85% down)
                            (width // 2, int(height * 0.8)),   # 80% down
                            (width // 2, int(height * 0.75)),  # 75% down
                        ]

                        for x, y in positions:
                            driver.tap([(x, y)])
                            self.logger.info(f"Tapped at position ({x}, {y}) for space")
                            time.sleep(0.3)  # Wait a bit to see if it worked
                            break  # Try just one position and move on

                        continue  # Continue with next character
                    except Exception as e:
                        self.logger.warning(f"Failed to tap space at common positions: {e}")
                        # Continue with next character

                # Handle numbers and special characters
                elif char.isdigit() or char in '.,?!@#$%^&*()-_=+[]{}|;:\'"/\\<>':
                    # For numbers and special characters, we might need to switch to the number/symbol keyboard
                    key_found = False

                    # For digits, try multiple approaches
                    if char.isdigit():
                        # First, try to find the number key directly using XPath (most reliable method)
                        number_found = False

                        # Try the specific XPath for the digit key
                        try:
                            key = driver.find_element(By.XPATH, f'//XCUIElementTypeKey[@name="{char}"]')
                            key.click()
                            self.logger.info(f"Tapped key '{char}' by XPath //XCUIElementTypeKey[@name=\"{char}\"]")
                            key_found = True
                            number_found = True
                            time.sleep(0.2)
                            continue
                        except Exception as e:
                            self.logger.debug(f"Key '{char}' not found by XPath //XCUIElementTypeKey[@name=\"{char}\"]: {e}")

                        # If not found, try to switch to number keyboard first
                        if not number_found:
                            try:
                                # Try to find the number toggle key
                                number_toggle_found = False

                                # Try using XPath for the number toggle key
                                for xpath in [
                                    '//XCUIElementTypeKey[@name="more"]',
                                    '//XCUIElementTypeKey[@name="123"]',
                                    '//XCUIElementTypeButton[@name="123"]',
                                    '//XCUIElementTypeKey[contains(@name, "more")]',
                                    '//XCUIElementTypeKey[contains(@name, "123")]',
                                    '//XCUIElementTypeKey[contains(@label, "more")]',
                                    '//XCUIElementTypeKey[contains(@label, "123")]',
                                    '//XCUIElementTypeButton[contains(@name, "more")]',
                                    '//XCUIElementTypeButton[contains(@name, "123")]'
                                ]:
                                    try:
                                        toggle_key = driver.find_element(By.XPATH, xpath)
                                        toggle_key.click()
                                        self.logger.info(f"Switched to number keyboard using XPath: {xpath}")
                                        time.sleep(0.5)  # Wait for keyboard to switch
                                        number_toggle_found = True
                                        break
                                    except Exception:
                                        continue

                                # If still not found, try tapping at common positions for the number toggle key
                                if not number_toggle_found:
                                    try:
                                        window_size = driver.get_window_size()
                                        width = window_size['width']
                                        height = window_size['height']

                                        # Common positions for the "123" or "more" key on iOS keyboards
                                        # Usually in the bottom left corner
                                        positions = [
                                            (width * 0.15, height * 0.85),  # Bottom left
                                            (width * 0.15, height * 0.8),   # Slightly higher
                                            (width * 0.2, height * 0.85),   # Slightly to the right
                                        ]

                                        for x, y in positions:
                                            driver.tap([(int(x), int(y))])
                                            self.logger.info(f"Tried tapping number toggle key at position ({int(x)}, {int(y)})")
                                            time.sleep(0.5)  # Wait for keyboard to switch

                                            # Check if we can now find a number key
                                            try:
                                                for num in ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9']:
                                                    try:
                                                        driver.find_element(By.XPATH, f'//XCUIElementTypeKey[@name="{num}"]')
                                                        self.logger.info(f"Found number key '{num}' after tapping toggle position")
                                                        number_toggle_found = True
                                                        break
                                                    except Exception:
                                                        continue

                                                if number_toggle_found:
                                                    break
                                            except Exception:
                                                pass
                                    except Exception as e:
                                        self.logger.debug(f"Failed to tap number toggle key at common positions: {e}")

                                # If toggle key found, try to find the digit again
                                if number_toggle_found:
                                    # Try the specific XPath for the digit key again
                                    try:
                                        key = driver.find_element(By.XPATH, f'//XCUIElementTypeKey[@name="{char}"]')
                                        key.click()
                                        self.logger.info(f"Tapped key '{char}' by XPath after switching to number keyboard")
                                        key_found = True
                                        number_found = True
                                        time.sleep(0.2)
                                        continue
                                    except Exception as e:
                                        self.logger.debug(f"Key '{char}' not found by XPath after switching to number keyboard: {e}")
                            except Exception as e:
                                self.logger.debug(f"Failed to switch to number keyboard: {e}")

                        # If still not found, try alternative XPath expressions
                        if not number_found:
                            for xpath in [
                                f'//XCUIElementTypeKey[@label="{char}"]',
                                f'//XCUIElementTypeButton[@name="{char}"]',
                                f'//XCUIElementTypeKey[contains(@name, "{char}")]',
                                f'//XCUIElementTypeButton[contains(@name, "{char}")]'
                            ]:
                                try:
                                    key = driver.find_element(By.XPATH, xpath)
                                    key.click()
                                    self.logger.info(f"Tapped key '{char}' using alternative XPath: {xpath}")
                                    key_found = True
                                    number_found = True
                                    time.sleep(0.2)
                                    break
                                except Exception:
                                    continue

                        # If still not found, try by name and accessibility ID
                        if not number_found:
                            # Try by name
                            try:
                                key = driver.find_element(By.NAME, char)
                                key.click()
                                self.logger.info(f"Tapped key '{char}' by name")
                                key_found = True
                                number_found = True
                                time.sleep(0.2)
                                continue
                            except Exception as e:
                                self.logger.debug(f"Key '{char}' not found by name: {e}")

                            # Try by accessibility ID
                            try:
                                key = driver.find_element(By.ACCESSIBILITY_ID, char)
                                key.click()
                                self.logger.info(f"Tapped key '{char}' by accessibility ID")
                                key_found = True
                                number_found = True
                                time.sleep(0.2)
                                continue
                            except Exception as e:
                                self.logger.debug(f"Key '{char}' not found by accessibility ID: {e}")

                        # If still not found, try direct tap at common positions for this number
                        if not number_found:
                            try:
                                window_size = driver.get_window_size()
                                width = window_size['width']
                                height = window_size['height']

                                # Map of number positions on standard iOS keyboard
                                number_positions = {
                                    '1': (width * 0.1, height * 0.4),
                                    '2': (width * 0.3, height * 0.4),
                                    '3': (width * 0.5, height * 0.4),
                                    '4': (width * 0.7, height * 0.4),
                                    '5': (width * 0.9, height * 0.4),
                                    '6': (width * 0.1, height * 0.5),
                                    '7': (width * 0.3, height * 0.5),
                                    '8': (width * 0.5, height * 0.5),
                                    '9': (width * 0.7, height * 0.5),
                                    '0': (width * 0.5, height * 0.6),
                                }

                                if char in number_positions:
                                    x, y = number_positions[char]
                                    driver.tap([(int(x), int(y))])
                                    self.logger.info(f"Tapped approximate position for number '{char}' at ({int(x)}, {int(y)})")
                                    key_found = True
                                    number_found = True
                                    time.sleep(0.3)
                                    continue
                            except Exception as e:
                                self.logger.warning(f"Failed to tap number '{char}' at predefined position: {e}")

                        # If we found the number, continue to next character
                        if number_found:
                            continue

                    # If not found, try to switch to number/symbol keyboard
                    if not key_found:
                        try:
                            # Try multiple methods to find the number toggle key
                            number_toggle_found = False

                            # Method 1: Try using XPath with contains for the more key
                            try:
                                more_key = driver.find_element(By.XPATH, '//XCUIElementTypeKey[contains(@name, "more") or contains(@name, "More") or contains(@name, "123")]')
                                more_key.click()
                                self.logger.info("Switched to number/symbol keyboard using key found by XPath contains")
                                time.sleep(0.5)  # Wait for keyboard to switch
                                number_toggle_found = True
                            except Exception as e:
                                self.logger.debug(f"More key not found by XPath contains: {e}")

                            # Method 2: Try specific XPath for the more key
                            if not number_toggle_found:
                                try:
                                    more_key = driver.find_element(By.XPATH, '//XCUIElementTypeKey[@name="more"]')
                                    more_key.click()
                                    self.logger.info("Switched to number/symbol keyboard using 'more' key XPath")
                                    time.sleep(0.5)  # Wait for keyboard to switch
                                    number_toggle_found = True
                                except Exception as e:
                                    self.logger.debug(f"More key not found by XPath: {e}")

                            # Method 3: Try by name with various labels
                            if not number_toggle_found:
                                for toggle_label in ['123', 'numbers', 'Numbers', 'more', 'More', '#+=']:
                                    try:
                                        number_toggle = driver.find_element(By.NAME, toggle_label)
                                        number_toggle.click()
                                        self.logger.info(f"Switched to number/symbol keyboard using '{toggle_label}' key")
                                        time.sleep(0.5)  # Wait for keyboard to switch
                                        number_toggle_found = True
                                        break
                                    except Exception:
                                        continue

                            # If we still couldn't find the number toggle, try a direct tap approach for numbers
                            if not number_toggle_found and char.isdigit():
                                try:
                                    self.logger.info(f"Trying direct tap approach for number '{char}'")
                                    # Get keyboard dimensions
                                    keyboard = None
                                    try:
                                        keyboard = driver.find_element(By.CLASS_NAME, 'XCUIElementTypeKeyboard')
                                    except Exception:
                                        try:
                                            keyboard = driver.find_element(By.XPATH, '//XCUIElementTypeKeyboard')
                                        except Exception:
                                            pass

                                    # Get screen dimensions
                                    window_size = driver.get_window_size()
                                    width = window_size['width']
                                    height = window_size['height']

                                    # Map of number positions on standard iOS keyboard
                                    # These are approximate positions based on standard iOS keyboard layout
                                    number_positions = {
                                        '1': (width * 0.1, height * 0.4),
                                        '2': (width * 0.3, height * 0.4),
                                        '3': (width * 0.5, height * 0.4),
                                        '4': (width * 0.7, height * 0.4),
                                        '5': (width * 0.9, height * 0.4),
                                        '6': (width * 0.1, height * 0.5),
                                        '7': (width * 0.3, height * 0.5),
                                        '8': (width * 0.5, height * 0.5),
                                        '9': (width * 0.7, height * 0.5),
                                        '0': (width * 0.5, height * 0.6),
                                    }

                                    # If we have a position for this number, tap it
                                    if char in number_positions:
                                        x, y = number_positions[char]
                                        driver.tap([(int(x), int(y))])
                                        self.logger.info(f"Tapped approximate position for number '{char}' at ({int(x)}, {int(y)})")
                                        key_found = True
                                        time.sleep(0.2)
                                        continue
                                except Exception as e:
                                    self.logger.warning(f"Failed to use direct tap approach for number '{char}': {e}")

                                # Now try to find the character again
                                try:
                                    key = driver.find_element(By.NAME, char)
                                    key.click()
                                    self.logger.info(f"Tapped key '{char}' on number/symbol keyboard")
                                    key_found = True
                                except Exception:
                                    # If still not found, try the second symbol page if available
                                    try:
                                        # Look for the second symbol page toggle
                                        symbol_toggle = None
                                        for toggle_label in ['#+=', 'more', 'More', 'symbols', 'Symbols']:
                                            try:
                                                symbol_toggle = driver.find_element(By.NAME, toggle_label)
                                                break
                                            except Exception:
                                                continue

                                        if symbol_toggle:
                                            symbol_toggle.click()
                                            self.logger.info("Switched to additional symbols keyboard")
                                            time.sleep(0.5)  # Wait for keyboard to switch

                                            # Try to find the character on this keyboard
                                            try:
                                                key = driver.find_element(By.NAME, char)
                                                key.click()
                                                self.logger.info(f"Tapped key '{char}' on additional symbols keyboard")
                                                key_found = True
                                            except Exception:
                                                # Switch back to letters keyboard
                                                try:
                                                    letters_toggle = driver.find_element(By.NAME, 'ABC')
                                                    letters_toggle.click()
                                                    self.logger.info("Switched back to letters keyboard")
                                                    time.sleep(0.5)
                                                except Exception:
                                                    self.logger.warning("Failed to switch back to letters keyboard")
                                    except Exception:
                                        # Switch back to letters keyboard
                                        try:
                                            letters_toggle = driver.find_element(By.NAME, 'ABC')
                                            letters_toggle.click()
                                            self.logger.info("Switched back to letters keyboard")
                                            time.sleep(0.5)
                                        except Exception:
                                            self.logger.warning("Failed to switch back to letters keyboard")
                        except Exception as e:
                            self.logger.warning(f"Failed to switch to number/symbol keyboard: {e}")

                    # If we still haven't found the key, try other methods
                    if not key_found:
                        # Try by XPath
                        try:
                            key = driver.find_element(By.XPATH, f'//XCUIElementTypeKey[@name="{char}"]')
                            key.click()
                            self.logger.info(f"Tapped key '{char}' by XPath")
                            key_found = True
                        except Exception:
                            pass

                        # Try sending directly to active element
                        if not key_found:
                            try:
                                active_element = driver.switch_to.active_element
                                active_element.send_keys(char)
                                self.logger.info(f"Sent character '{char}' to active element")
                                key_found = True
                            except Exception:
                                pass

                # Handle regular characters (letters)
                else:
                    # Try multiple methods to find and tap the character key
                    key_found = False

                    # Method 1: Try by accessibility ID
                    try:
                        key = driver.find_element(By.ACCESSIBILITY_ID, char)
                        key.click()
                        self.logger.info(f"Tapped key '{char}' by accessibility ID")
                        key_found = True
                    except Exception as e:
                        self.logger.debug(f"Key '{char}' not found by accessibility ID: {e}")

                    # Method 2: Try by name
                    if not key_found:
                        try:
                            key = driver.find_element(By.NAME, char)
                            key.click()
                            self.logger.info(f"Tapped key '{char}' by name")
                            key_found = True
                        except Exception as e:
                            self.logger.debug(f"Key '{char}' not found by name: {e}")

                    # Method 3: Try by XPath
                    if not key_found:
                        try:
                            key = driver.find_element(By.XPATH, f'//XCUIElementTypeKey[@name="{char}"]')
                            key.click()
                            self.logger.info(f"Tapped key '{char}' by XPath")
                            key_found = True
                        except Exception as e:
                            self.logger.debug(f"Key '{char}' not found by XPath: {e}")

                    # Handle case for uppercase letters
                    if not key_found and char.isalpha():
                        # If it's a letter and we couldn't find it, it might be because of case
                        # Try the opposite case
                        alt_char = char.upper() if char.islower() else char.lower()

                        try:
                            # First check if we need to tap shift for uppercase
                            if char.isupper():
                                try:
                                    shift_key = driver.find_element(By.ACCESSIBILITY_ID, 'shift')
                                    shift_key.click()
                                    self.logger.info("Tapped shift key")
                                    time.sleep(0.2)
                                except Exception as shift_e:
                                    self.logger.warning(f"Shift key not found by accessibility ID: {shift_e}")

                                    # Try by name
                                    try:
                                        shift_key = driver.find_element(By.NAME, 'shift')
                                        shift_key.click()
                                        self.logger.info("Tapped shift key by name")
                                        time.sleep(0.2)
                                    except Exception as e:
                                        self.logger.warning(f"Shift key not found by name: {e}")

                                        # Try by XPath
                                        try:
                                            shift_key = driver.find_element(By.XPATH, '//XCUIElementTypeKey[contains(@name, "shift") or contains(@name, "Shift")]')
                                            shift_key.click()
                                            self.logger.info("Tapped shift key by XPath")
                                            time.sleep(0.2)
                                        except Exception as e:
                                            self.logger.warning(f"Shift key not found by XPath: {e}")

                            # Try to find the key with the alternate case
                            key = driver.find_element(By.NAME, alt_char)
                            key.click()
                            self.logger.info(f"Tapped key '{alt_char}' (alternate case)")
                            key_found = True
                        except Exception as e:
                            self.logger.debug(f"Key '{alt_char}' (alternate case) not found: {e}")

                    # If still not found, try to find any key that contains the character
                    if not key_found:
                        try:
                            key = driver.find_element(By.XPATH, f'//XCUIElementTypeKey[contains(@name, "{char}")]')
                            key.click()
                            self.logger.info(f"Tapped key containing '{char}' by XPath")
                            key_found = True
                        except Exception as e:
                            self.logger.debug(f"No key containing '{char}' found: {e}")

                    if not key_found:
                        # Try to send the character directly using send_keys to the active element
                        try:
                            active_element = driver.switch_to.active_element
                            active_element.send_keys(char)
                            self.logger.info(f"Sent character '{char}' to active element")
                            key_found = True
                        except Exception as e:
                            self.logger.warning(f"Failed to send character '{char}' to active element: {e}")

                            # Try using driver.execute_script with mobile: type
                            try:
                                driver.execute_script('mobile: type', {'text': char})
                                self.logger.info(f"Typed character '{char}' using mobile: type")
                                key_found = True
                            except Exception as e:
                                self.logger.warning(f"Failed to type character '{char}' using mobile: type: {e}")

                                # Try using driver.execute_script with mobile: typeText
                                try:
                                    driver.execute_script('mobile: typeText', {'text': char})
                                    self.logger.info(f"Typed character '{char}' using mobile: typeText")
                                    key_found = True
                                except Exception as e:
                                    self.logger.warning(f"Failed to type character '{char}' using mobile: typeText: {e}")

                # Small delay between key presses
                time.sleep(0.2)

            self.logger.info(f"Completed keyboard simulation for text: '{text}'")
            return True

        except Exception as e:
            self.logger.error(f"Error during keyboard simulation: {e}")
            import traceback
            self.logger.error(traceback.format_exc())
            return False

    def _has_selenium_support(self):
        """Check if Selenium WebDriverWait is available"""
        try:
            from selenium.webdriver.support.ui import WebDriverWait
            return True
        except ImportError:
            return False
