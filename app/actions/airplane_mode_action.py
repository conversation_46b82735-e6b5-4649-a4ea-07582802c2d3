from base_action import BaseAction

class AirplaneModeAction(BaseAction):
    """Handler for toggling airplane mode action"""
    
    def execute(self, params):
        """
        Execute airplane mode toggle action
        
        Args:
            params: Dictionary containing:
                - enabled: <PERSON><PERSON><PERSON> indicating if airplane mode should be enabled or disabled
                
        Returns:
            dict: Result with status and message
        """
        if not self.controller:
            return {"status": "error", "message": "No device controller available"}
        
        enabled = params.get('enabled', False)
        
        try:
            # Get the platform if available
            platform = getattr(self.controller, 'platform_name', None)
            
            # Check if we have an Appium driver
            if hasattr(self.controller, 'driver') and self.controller.driver:
                # For Android, use network_connection setting
                if platform and platform.lower() == 'android':
                    # Get current network settings
                    current_connection = self.controller.driver.network_connection
                    
                    # Network connection is a bitmask:
                    # 1 - Airplane Mode
                    # 2 - Wi-Fi
                    # 4 - Data
                    
                    if enabled:
                        # Turn ON airplane mode (set bit 1 to 1, set others to 0)
                        new_connection = 1
                    else:
                        # Turn OFF airplane mode (set bit 1 to 0, maintain others)
                        # If already enabled, restore Wi-Fi and Data (bits 2 and 4)
                        if current_connection & 1:  # If airplane mode is on
                            new_connection = 6  # Enable Wi-Fi and Data
                        else:
                            # No change needed
                            new_connection = current_connection
                    
                    # Update connection settings
                    self.controller.driver.set_network_connection(new_connection)
                    
                    mode_status = "enabled" if enabled else "disabled"
                    self.logger.info(f"Airplane mode {mode_status} on Android device")
                    return {"status": "success", "message": f"Airplane mode {mode_status}"}
                
                # For iOS, we need to use mobile: commands
                elif platform and platform.lower() == 'ios':
                    # In iOS real devices, we need to use mobile: commands
                    # This requires proper permissions and entitlements
                    try:
                        if enabled:
                            self.controller.driver.execute_script('mobile: setAirplaneMode', {'enabled': True})
                        else:
                            self.controller.driver.execute_script('mobile: setAirplaneMode', {'enabled': False})
                        
                        mode_status = "enabled" if enabled else "disabled"
                        self.logger.info(f"Airplane mode {mode_status} on iOS device")
                        return {"status": "success", "message": f"Airplane mode {mode_status}"}
                    except Exception as ios_err:
                        self.logger.error(f"Failed to set airplane mode on iOS: {ios_err}")
                        return {"status": "error", "message": f"Failed to set airplane mode on iOS: {str(ios_err)}. This may require specific entitlements."}
                else:
                    return {"status": "error", "message": f"Unsupported platform for airplane mode: {platform}"}
            else:
                return {"status": "error", "message": "Controller does not have an active driver"}
                
        except Exception as e:
            self.logger.error(f"Error setting airplane mode: {e}")
            return {"status": "error", "message": f"Airplane mode action failed: {str(e)}"} 