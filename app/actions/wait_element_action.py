from base_action import BaseAction

class WaitElementAction(BaseAction):
    """Handler for waiting for elements identified by locator to appear or become visible"""

    def execute(self, params):
        """
        Execute wait for element action

        Args:
            params: Dictionary containing:
                - locator_type: Type of locator (id, xpath, accessibility_id, etc.)
                - locator_value: Value of the locator
                - wait_type: (Optional) Type of wait - 'visible', 'present', 'clickable', 'invisible' (default: 'visible')
                - timeout: (Optional) Maximum time to wait in seconds (default: 20)

        Returns:
            dict: Result with status and message
        """
        if not self.controller:
            return {"status": "error", "message": "No device controller available"}

        locator_type = params.get('locator_type')
        locator_value = params.get('locator_value')
        wait_type = params.get('wait_type', 'visible').lower()  # Default to waiting for visibility
        # Get timeout from global settings or use provided value
        default_timeout = self.get_global_timeout()
        timeout = params.get('timeout', default_timeout)

        if not locator_type or not locator_value:
            return {"status": "error", "message": "Missing required parameters: locator_type and locator_value"}

        # Validate wait_type
        valid_wait_types = ['visible', 'present', 'clickable', 'invisible']
        if wait_type not in valid_wait_types:
            return {
                "status": "error",
                "message": f"Invalid wait_type: '{wait_type}'. Must be one of: {', '.join(valid_wait_types)}"
            }

        try:
            # Check if controller has a specific wait_element method
            if hasattr(self.controller, 'wait_element'):
                result = self.controller.wait_element(
                    locator_type=locator_type,
                    locator_value=locator_value,
                    wait_type=wait_type,
                    timeout=timeout
                )

                # Handle different return types
                if isinstance(result, dict):
                    return result
                elif isinstance(result, bool):
                    if result:
                        return {
                            "status": "success",
                            "message": f"Element {wait_type} wait successful: {locator_type}='{locator_value}'"
                        }
                    else:
                        return {
                            "status": "error",
                            "message": f"Wait for element {wait_type} timed out: {locator_type}='{locator_value}'"
                        }
                elif result is not None:
                    # Assume success if any non-None/False value is returned
                    return {
                        "status": "success",
                        "message": f"Element {wait_type} wait successful: {locator_type}='{locator_value}'"
                    }
                else:
                    # None result typically means timeout
                    return {
                        "status": "error",
                        "message": f"Wait for element {wait_type} timed out: {locator_type}='{locator_value}'"
                    }

            # If the controller has an Appium driver directly available
            elif hasattr(self.controller, 'driver') and self.controller.driver:
                driver = self.controller.driver

                # Try to use Selenium WebDriverWait if available
                if self._has_selenium_support():
                    try:
                        from selenium.webdriver.support.ui import WebDriverWait
                        from selenium.webdriver.support import expected_conditions as EC
                        from selenium.webdriver.common.by import By
                        from selenium.common.exceptions import TimeoutException, NoSuchElementException

                        # Map locator types to Selenium/Appium By types
                        locator_map = {
                            'id': By.ID,
                            'xpath': By.XPATH,
                            'name': By.NAME,
                            'class': By.CLASS_NAME,
                            'accessibility_id': By.ACCESSIBILITY_ID,
                            'ios_predicate': By.IOS_PREDICATE,
                            'android_uiautomator': By.ANDROID_UIAUTOMATOR,
                            'ios_class_chain': By.IOS_CLASS_CHAIN
                        }

                        by_type = locator_map.get(locator_type.lower())
                        if not by_type:
                            by_type = locator_type  # Use as-is if not in mapping

                        # Choose the appropriate expected condition based on wait_type
                        wait = WebDriverWait(driver, timeout)
                        if wait_type == 'visible':
                            wait.until(EC.visibility_of_element_located((by_type, locator_value)))
                        elif wait_type == 'present':
                            wait.until(EC.presence_of_element_located((by_type, locator_value)))
                        elif wait_type == 'clickable':
                            wait.until(EC.element_to_be_clickable((by_type, locator_value)))
                        elif wait_type == 'invisible':
                            wait.until(EC.invisibility_of_element_located((by_type, locator_value)))

                        return {
                            "status": "success",
                            "message": f"Element {wait_type} wait successful: {locator_type}='{locator_value}'"
                        }
                    except TimeoutException:
                        return {
                            "status": "error",
                            "message": f"Wait for element {wait_type} timed out: {locator_type}='{locator_value}'"
                        }
                    except Exception as e:
                        self.logger.error(f"Error during wait: {e}")
                        return {
                            "status": "error",
                            "message": f"Wait failed: {str(e)}"
                        }
                else:
                    # Fallback to manual polling if WebDriverWait is not available
                    import time
                    start_time = time.time()

                    while time.time() - start_time < timeout:
                        try:
                            # Try to find the element
                            # Use modern AppiumBy locator constants
                            try:
                                from appium.webdriver.common.appiumby import AppiumBy

                                # Map locator type to AppiumBy constants
                                locator_map = {
                                    'accessibility_id': AppiumBy.ACCESSIBILITY_ID,
                                    'ios_predicate': AppiumBy.IOS_PREDICATE,
                                    'android_uiautomator': AppiumBy.ANDROID_UIAUTOMATOR,
                                    'ios_class_chain': AppiumBy.IOS_CLASS_CHAIN,
                                    'id': AppiumBy.ID,
                                    'xpath': AppiumBy.XPATH,
                                    'name': AppiumBy.NAME,
                                    'class': AppiumBy.CLASS_NAME,
                                    'tag': AppiumBy.TAG_NAME,
                                    'css': AppiumBy.CSS_SELECTOR,
                                    'link_text': AppiumBy.LINK_TEXT,
                                    'partial_link_text': AppiumBy.PARTIAL_LINK_TEXT
                                }

                                by_type = locator_map.get(locator_type.lower())
                                if by_type:
                                    element = driver.find_element(by_type, locator_value)
                                else:
                                    # Try to use the locator type as-is
                                    element = driver.find_element(locator_type, locator_value)
                            except ImportError:
                                # Fallback for older versions
                                # Handle Appium-specific locator types
                                if locator_type.lower() == 'accessibility_id':
                                    element = driver.find_element_by_accessibility_id(locator_value)
                                elif locator_type.lower() == 'ios_predicate':
                                    element = driver.find_element_by_ios_predicate(locator_value)
                                elif locator_type.lower() == 'android_uiautomator':
                                    element = driver.find_element_by_android_uiautomator(locator_value)
                                elif locator_type.lower() == 'ios_class_chain':
                                    element = driver.find_element_by_ios_class_chain(locator_value)
                                # Standard Selenium locators
                                elif locator_type.lower() == 'id':
                                    element = driver.find_element_by_id(locator_value)
                                elif locator_type.lower() == 'xpath':
                                    element = driver.find_element_by_xpath(locator_value)
                                elif locator_type.lower() == 'name':
                                    element = driver.find_element_by_name(locator_value)
                                elif locator_type.lower() == 'class':
                                    element = driver.find_element_by_class_name(locator_value)
                                else:
                                    # Old fallback: try the modern method with selenium By constants
                                    try:
                                        from selenium.webdriver.common.by import By
                                        locator_map = {
                                            'id': By.ID,
                                            'xpath': By.XPATH,
                                            'name': By.NAME,
                                            'class': By.CLASS_NAME,
                                            'tag': By.TAG_NAME,
                                            'css': By.CSS_SELECTOR,
                                            'link_text': By.LINK_TEXT,
                                            'partial_link_text': By.PARTIAL_LINK_TEXT
                                        }
                                        by_type = locator_map.get(locator_type.lower())
                                        if by_type:
                                            element = driver.find_element(by_type, locator_value)
                                        else:
                                            element = None
                                    except Exception:
                                        element = None

                            # If we're waiting for the element to be invisible and it's not found,
                            # that means it's already invisible (success case)
                            if wait_type == 'invisible' and element is None:
                                return {
                                    "status": "success",
                                    "message": f"Element is invisible: {locator_type}='{locator_value}'"
                                }

                            # For other wait types, not finding the element means keep waiting
                            if element is None:
                                time.sleep(0.5)
                                continue

                            # Check additional conditions based on wait_type
                            if wait_type == 'visible':
                                if element.is_displayed():
                                    return {
                                        "status": "success",
                                        "message": f"Element is visible: {locator_type}='{locator_value}'"
                                    }
                            elif wait_type == 'present':
                                # Element was found, so it's present
                                return {
                                    "status": "success",
                                    "message": f"Element is present: {locator_type}='{locator_value}'"
                                }
                            elif wait_type == 'clickable':
                                if element.is_displayed() and element.is_enabled():
                                    return {
                                        "status": "success",
                                        "message": f"Element is clickable: {locator_type}='{locator_value}'"
                                    }
                            elif wait_type == 'invisible':
                                if not element.is_displayed():
                                    return {
                                        "status": "success",
                                        "message": f"Element is invisible: {locator_type}='{locator_value}'"
                                    }

                            # Condition not met, wait more
                            time.sleep(0.5)

                        except NoSuchElementException:
                            # If we're waiting for invisibility, this is a success
                            if wait_type == 'invisible':
                                return {
                                    "status": "success",
                                    "message": f"Element is invisible: {locator_type}='{locator_value}'"
                                }
                            # For other wait types, keep waiting
                            time.sleep(0.5)
                        except Exception as e:
                            self.logger.error(f"Error during manual polling: {e}")
                            # Continue waiting despite errors
                            time.sleep(0.5)

                    # If we get here, we timed out
                    return {
                        "status": "error",
                        "message": f"Wait for element {wait_type} timed out: {locator_type}='{locator_value}'"
                    }

            else:
                return {
                    "status": "error",
                    "message": "Device controller doesn't support element wait operations"
                }

        except Exception as e:
            self.logger.error(f"Error executing wait element action: {e}")
            return {"status": "error", "message": f"Wait element action failed: {str(e)}"}

    def _has_selenium_support(self):
        """Check if Selenium WebDriverWait is available"""
        try:
            from selenium.webdriver.support.ui import WebDriverWait
            return True
        except ImportError:
            return False