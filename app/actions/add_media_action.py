from base_action import BaseAction
import os
import sys
import logging
import re
import mimetypes

# Use root-level config
sys.path.insert(0, os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))))
try:
    import config
except ImportError:
    logging.getLogger('AddMediaAction').error("Could not import config.py from root.")
    config = type('config', (), {})  # Fallback empty config

class AddMediaAction(BaseAction):
    """Handler for adding media files to the device"""
    
    def execute(self, params=None, context=None):
        """
        Executes the action with the given parameters.
        
        Args:
            params (dict): Parameters for the action.
            context (dict): Context data for the action.
            
        Returns:
            dict: Result of the action.
        """
        if not params:
            params = {}
            
        # Get the source path from parameters or use the default
        source_path = params.get("source_path", "")
        if not source_path:
            return {"status": "error", "message": "source_path is required"}
            
        # Check if the source path exists
        if not os.path.exists(source_path):
            return {"status": "error", "message": f"File not found: {source_path}"}
            
        # Get platform from context
        platform = context.get("platform", "")
        device_id = context.get("device_id", "")
        device_name = context.get("device_name", "")
        
        # Log context data
        self.logger.info(f"Platform: {platform}")
        self.logger.info(f"Device ID: {device_id}")
        self.logger.info(f"Device Name: {device_name}")
        
        # Check if platform is iOS or Android
        if platform.lower() == "ios":
            # For iOS, we use a completely different approach for physical devices
            self.logger.info(f"iOS physical device detected")
            
            # For physical iOS devices, we'll use a custom approach
            # The process involves copying the file to the iOS device using file-relay or similar mechanism
            try:
                # Use tidevice to transfer files to iOS device
                import subprocess
                
                # Check if tidevice is available
                try:
                    subprocess.run(['tidevice', '--version'], 
                                  stdout=subprocess.PIPE, 
                                  stderr=subprocess.PIPE, 
                                  check=True)
                except (subprocess.SubprocessError, FileNotFoundError):
                    return {"status": "error", "message": "tidevice not installed. Install with 'pip install -U tidevice'"}
                
                # Create a temporary directory where we'll extract files
                app_bundle_id = "au.com.kmart.staging"  # Change as needed
                
                # Use tidevice to push the file to the media directory
                # Documentation: https://github.com/alibaba/tidevice
                # Format: tidevice --udid DEVICE_ID push SOURCE_PATH DEST_PATH
                cmd = ["tidevice"]
                if device_id:
                    cmd.extend(["--udid", device_id])
                
                # For media files, we typically need to push to a location the app can access
                # This is a simplified approach; actual implementation may vary
                cmd.extend(["push", source_path, f"/tmp/media/{os.path.basename(source_path)}"])
                
                self.logger.info(f"Running command: {' '.join(cmd)}")
                result = subprocess.run(cmd, capture_output=True, text=True, check=True)
                
                return {"status": "success", "message": f"File transferred to iOS device: {os.path.basename(source_path)}"}
                
            except Exception as e:
                self.logger.error(f"Error transferring file to iOS device: {str(e)}")
                return {"status": "error", "message": f"Failed to transfer file: {str(e)}"}
        
        elif platform.lower() == "android":
            # For Android, we use ADB to push the file
            try:
                # Use adb to push the file to the device
                import subprocess
                
                # Define target path on device
                target_path = f"/sdcard/Download/{os.path.basename(source_path)}"
                
                # Build the adb command
                adb_cmd = ["adb"]
                if device_id:
                    adb_cmd.extend(["-s", device_id])
                adb_cmd.extend(["push", source_path, target_path])
                
                # Execute the command
                self.logger.info(f"Running command: {' '.join(adb_cmd)}")
                result = subprocess.run(adb_cmd, capture_output=True, text=True, check=True)
                
                return {"status": "success", "message": f"File pushed to Android device: {target_path}"}
            
            except Exception as e:
                self.logger.error(f"Error pushing file to Android device: {str(e)}")
                return {"status": "error", "message": f"Failed to push file: {str(e)}"}
        
        else:
            return {"status": "error", "message": f"Unsupported platform: {platform}"} 