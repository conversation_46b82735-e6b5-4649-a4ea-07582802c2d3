from base_action import BaseAction

class SendKeysAction(BaseAction):
    """Handler for sending keys to elements identified by locator"""

    def execute(self, params):
        """
        Execute send keys action

        Args:
            params: Dictionary containing:
                - locator_type: Type of locator (id, xpath, accessibility_id, etc.)
                - locator_value: Value of the locator
                - text: Text to send
                - clear_first: (Optional) Whether to clear input field first (default: False)
                - timeout: (Optional) Maximum time to wait for element in seconds

        Returns:
            dict: Result with status and message
        """
        if not self.controller:
            return {"status": "error", "message": "No device controller available"}

        locator_type = params.get('locator_type')
        locator_value = params.get('locator_value')
        text = params.get('text')
        clear_first = params.get('clear_first', False)
        timeout = params.get('timeout', 15)  # Default timeout of 15 seconds

        if not locator_type or not locator_value:
            return {"status": "error", "message": "Missing required parameters: locator_type and locator_value"}

        if text is None:
            return {"status": "error", "message": "Missing required parameter: text"}

        try:
            # Use the controller's input_text method which already handles send_keys
            result = self.controller.input_text(
                locator_type=locator_type,
                locator_value=locator_value,
                text=text,
                clear_first=clear_first,
                timeout=timeout
            )

            # Update the message to reflect "Send Keys" action
            if result.get('status') == 'success':
                result['message'] = result['message'].replace('Text input successful', 'Send keys successful')

            return result

        except Exception as e:
            self.logger.error(f"Error executing send keys action: {e}")
            return {"status": "error", "message": f"Send keys action failed: {str(e)}"}
