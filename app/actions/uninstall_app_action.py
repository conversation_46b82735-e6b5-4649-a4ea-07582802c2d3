from base_action import BaseAction

class UninstallAppAction(BaseAction):
    """Handler for uninstalling apps from the device"""

    def execute(self, params):
        """
        Execute uninstall app action

        Args:
            params: Dictionary containing:
                - package_id: The package ID or bundle ID of the app to uninstall

        Returns:
            dict: Result with status and message
        """
        if not self.controller:
            return {"status": "error", "message": "No device controller available"}

        package_id = params.get('package_id')
        if not package_id:
            return {"status": "error", "message": "Package ID is required"}

        try:
            # Call the controller's uninstall_app method
            result = self.controller.uninstall_app(package_id)

            # Take a screenshot after action
            screenshot_path = self.take_screenshot_after_action()

            # Handle different result formats
            if isinstance(result, dict):
                # If result is already a dict, just add screenshot if available
                if screenshot_path:
                    result['screenshot'] = screenshot_path
                return result
            elif result is True:
                # If result is True, return success
                response = {
                    "status": "success",
                    "message": f"Successfully uninstalled app {package_id}"
                }
                if screenshot_path:
                    response['screenshot'] = screenshot_path
                return response
            elif result is False:
                # If result is False, return error
                return {
                    "status": "error",
                    "message": f"Failed to uninstall app {package_id}"
                }
            else:
                # Handle any other result type
                self.logger.warning(f"Unexpected result type from uninstall_app: {type(result)}")
                return {
                    "status": "warning",
                    "message": f"Unexpected result from uninstall operation for {package_id}",
                    "screenshot": screenshot_path
                }

        except Exception as e:
            self.logger.error(f"Error uninstalling app {package_id}: {e}")
            return {"status": "error", "message": f"Failed to uninstall app: {str(e)}"}
