from base_action import BaseAction
from airtest.core.helper import log
import logging
import os
import sys
import time

class AddLogAction(BaseAction):
    """Handler for adding log entries with optional screenshots"""

    def execute(self, params):
        """
        Execute add log action

        Args:
            params: Dictionary containing:
                - message: The log message to add
                - take_screenshot: (Optional) Whether to take a screenshot (default: True)
                - action_id: (Optional) Action ID for screenshot naming

        Returns:
            dict: Result with status and message
        """
        if not self.controller:
            return {"status": "error", "message": "No device controller available"}

        message = params.get('message', '')
        take_screenshot = params.get('take_screenshot', True)
        action_id = params.get('action_id')

        # If no action_id is provided, generate one
        if not action_id:
            import random
            import string
            chars = string.ascii_letters + string.digits
            action_id = ''.join(random.choice(chars) for _ in range(10))
            self.logger.info(f"Generated action_id for addLog action: {action_id}")
        else:
            # If action_id starts with 'al_', remove the prefix for consistent naming
            if action_id.startswith('al_'):
                self.logger.info(f"Removing 'al_' prefix from action_id: {action_id}")
                action_id = action_id[3:]
                self.logger.info(f"New action_id without prefix: {action_id}")
            # Log the final action_id that will be used
            self.logger.info(f"Using action_id for screenshot: {action_id}")

        # Store the action_id in the result to ensure it's returned to the client
        result_action_id = action_id

        if not message:
            return {"status": "error", "message": "Missing message parameter"}

        try:
            # Use Airtest's log function to add a log entry with optional screenshot
            self.logger.info(f"Adding log entry: {message}")

            # Take screenshot if requested
            screenshot_path = None
            if take_screenshot:
                try:
                    # Take screenshot using the device controller
                    screenshot_result = self.controller.take_screenshot(action_id=action_id)
                    if screenshot_result.get('status') == 'success':
                        screenshot_path = screenshot_result.get('path')
                        self.logger.info(f"Screenshot taken successfully: {screenshot_path}")
                    else:
                        self.logger.warning(f"Failed to take screenshot: {screenshot_result.get('message', 'Unknown error')}")
                except Exception as screenshot_error:
                    self.logger.error(f"Error taking screenshot: {screenshot_error}")

            # Use Airtest log function if available
            try:
                log(message, screenshot=screenshot_path is not None)
                self.logger.info(f"Added log entry using Airtest: {message}")
            except Exception as airtest_error:
                self.logger.warning(f"Could not use Airtest log function: {airtest_error}")
                # Fallback to regular logging
                self.logger.info(f"Log entry (fallback): {message}")

            return {
                "status": "success",
                "message": message,
                "screenshot": screenshot_path,
                "screenshot_filename": os.path.basename(screenshot_path) if screenshot_path else None,
                "action_id": result_action_id
            }

        except Exception as e:
            self.logger.error(f"Error executing add log action: {e}")
            return {"status": "error", "message": f"Add log action failed: {str(e)}"}
