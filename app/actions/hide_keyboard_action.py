from base_action import BaseAction

class HideKeyboardAction(BaseAction):
    """Handler for hiding the keyboard action"""
    
    def execute(self, params):
        """
        Execute hide keyboard action
        
        Args:
            params: Empty dictionary, no parameters needed
                
        Returns:
            dict: Result with status and message
        """
        if not self.controller:
            return {"status": "error", "message": "No device controller available"}
        
        try:
            # Access the Appium driver
            if hasattr(self.controller, 'driver') and self.controller.driver:
                self.logger.info("Attempting to hide keyboard")
                
                # Get the platform if available
                platform = getattr(self.controller, 'platform_name', None)
                
                # Hide keyboard
                self.controller.driver.hide_keyboard()
                
                self.logger.info(f"Successfully hid keyboard on {platform if platform else 'device'}")
                return {"status": "success", "message": "Keyboard hidden successfully"}
            else:
                return {"status": "error", "message": "Controller does not have an active driver"}
                
        except Exception as e:
            self.logger.error(f"Error hiding keyboard: {e}")
            return {"status": "error", "message": f"Hide keyboard action failed: {str(e)}"} 