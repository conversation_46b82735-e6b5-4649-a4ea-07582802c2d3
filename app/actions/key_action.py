from base_action import BaseAction

class KeyAction(BaseAction):
    """Handler for key press actions"""
    
    def execute(self, params):
        """
        Execute key press action
        
        Args:
            params: Dictionary containing:
                - key: The key code to press
                
        Returns:
            dict: Result with status and message
        """
        if not self.controller:
            return {"status": "error", "message": "No device controller available"}
        
        key = params.get('key')
        if key is None:
            return {"status": "error", "message": "Missing key code"}
        
        try:
            # Get the platform if available
            platform = getattr(self.controller, 'platform_name', None)
            
            # For AppiumDeviceController, use press_keycode
            if hasattr(self.controller, 'press_keycode'):
                self.logger.info(f"Using press_keycode method for platform: {platform}")
                
                # For Android, always use press_keycode with int keycode
                if platform and platform.lower() == 'android':
                    try:
                        key_int = int(key)
                    except ValueError:
                        return {"status": "error", "message": f"Android requires numeric key code, got: {key}"}
                    
                    result = self.controller.press_keycode(key_int)
                else:
                    # For other platforms, try as-is
                    result = self.controller.press_keycode(key)
                
                # Return the result from press_keycode
                if isinstance(result, dict):
                    return result
                elif result is True:
                    return {"status": "success", "message": f"Pressed key: {key}"}
                else:
                    return {"status": "error", "message": f"Failed to press key: {key}"}
            
            # Fall back to press_key for other controller types
            elif hasattr(self.controller, 'press_key'):
                self.logger.info("Using press_key method")
                result = self.controller.press_key(key)
                
                if isinstance(result, dict):
                    return result
                elif result is True:
                    return {"status": "success", "message": f"Pressed key: {key}"}
                else:
                    return {"status": "error", "message": f"Failed to press key: {key}"}
            
            # Try driver.press_keycode directly if driver is available
            elif hasattr(self.controller, 'driver') and self.controller.driver:
                self.logger.info("Attempting to use driver.press_keycode directly")
                try:
                    # Try to convert to int for Android
                    key_int = int(key)
                    self.controller.driver.press_keycode(key_int)
                    return {"status": "success", "message": f"Pressed key: {key} (using driver directly)"}
                except (ValueError, Exception) as e:
                    self.logger.error(f"Error using driver.press_keycode: {e}")
                    return {"status": "error", "message": f"Failed to press key: {str(e)}"}
            
            # No suitable method found
            else:
                return {"status": "error", "message": "Controller does not support key press operations"}
                
        except Exception as e:
            self.logger.error(f"Error executing key press action: {e}")
            return {"status": "error", "message": f"Key press action failed: {str(e)}"} 