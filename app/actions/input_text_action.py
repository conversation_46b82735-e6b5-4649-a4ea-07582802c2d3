from base_action import BaseAction
from app.utils.parameter_utils import substitute_parameters

class InputTextAction(BaseAction):
    """Handler for inputting text into elements identified by locator"""

    def execute(self, params):
        """
        Execute text input action

        Args:
            params: Dictionary containing:
                - locator_type: Type of locator (id, xpath, accessibility_id, etc.)
                - locator_value: Value of the locator
                - text: Text to input
                - clear_first: (Optional) Whether to clear input field first (default: True)
                - timeout: (Optional) Maximum time to wait for element in seconds

        Returns:
            dict: Result with status and message
        """
        if not self.controller:
            return {"status": "error", "message": "No device controller available"}

        locator_type = params.get('locator_type')
        locator_value = params.get('locator_value')

        # Get the text and apply parameter substitution
        original_text = params.get('text')
        text = substitute_parameters(original_text)

        clear_first = params.get('clear_first', True)
        timeout = params.get('timeout', 15)  # Default timeout of 15 seconds

        # Log if parameter substitution occurred
        if text != original_text:
            self.logger.info(f"Parameter substitution applied: '{original_text}' -> '{text}'")

        if not locator_type or not locator_value:
            return {"status": "error", "message": "Missing required parameters: locator_type and locator_value"}

        if text is None:
            return {"status": "error", "message": "Missing required parameter: text"}

        try:
            # Check if controller has a specific input_text method
            if hasattr(self.controller, 'input_text'):
                result = self.controller.input_text(
                    locator_type=locator_type,
                    locator_value=locator_value,
                    text=text,
                    clear_first=clear_first,
                    timeout=timeout
                )

                # Handle different return types
                if isinstance(result, dict):
                    return result
                elif isinstance(result, bool):
                    if result:
                        return {
                            "status": "success",
                            "message": f"Text input successful: '{text}' into {locator_type}='{locator_value}'"
                        }
                    else:
                        return {
                            "status": "error",
                            "message": f"Failed to input text into element: {locator_type}='{locator_value}'"
                        }
                elif result is not None:
                    # Assume success if any non-None/False value is returned
                    return {
                        "status": "success",
                        "message": f"Text input successful: '{text}' into {locator_type}='{locator_value}'"
                    }
                else:
                    # None result typically means element not found or input failed
                    return {
                        "status": "error",
                        "message": f"Failed to input text into element: {locator_type}='{locator_value}'"
                    }

            # If the controller has an Appium driver directly available
            elif hasattr(self.controller, 'driver') and self.controller.driver:
                driver = self.controller.driver

                # First, find the element
                element = None
                try:
                    if self._has_selenium_support():
                        try:
                            from selenium.webdriver.support.ui import WebDriverWait
                            from selenium.webdriver.support import expected_conditions as EC
                            from selenium.webdriver.common.by import By
                            from selenium.common.exceptions import TimeoutException

                            # Map locator types to Selenium/Appium By types
                            locator_map = {
                                'id': By.ID,
                                'xpath': By.XPATH,
                                'name': By.NAME,
                                'class': By.CLASS_NAME,
                                'accessibility_id': 'accessibility id',  # Appium specific
                                'ios_predicate': '-ios predicate string',  # iOS specific
                                'android_uiautomator': '-android uiautomator',  # Android specific
                                'ios_class_chain': '-ios class chain'  # iOS specific
                            }

                            by_type = locator_map.get(locator_type.lower())
                            if not by_type:
                                by_type = locator_type  # Use as-is if not in mapping

                            # Wait for element to be visible and enabled
                            element = WebDriverWait(driver, timeout).until(
                                EC.element_to_be_clickable((by_type, locator_value))
                            )
                        except TimeoutException:
                            return {
                                "status": "error",
                                "message": f"Element not found or not interactable within timeout: {locator_type}='{locator_value}'"
                            }
                        except Exception as inner_e:
                            self.logger.error(f"Error waiting for element: {inner_e}")
                            return {
                                "status": "error",
                                "message": f"Wait failed: {str(inner_e)}"
                            }
                    else:
                        # Try to find element without explicit wait
                        # Handle Appium-specific locator types
                        if locator_type.lower() == 'accessibility_id':
                            element = driver.find_element_by_accessibility_id(locator_value)
                        elif locator_type.lower() == 'ios_predicate':
                            element = driver.find_element_by_ios_predicate(locator_value)
                        elif locator_type.lower() == 'android_uiautomator':
                            element = driver.find_element_by_android_uiautomator(locator_value)
                        elif locator_type.lower() == 'ios_class_chain':
                            element = driver.find_element_by_ios_class_chain(locator_value)
                        # Standard Selenium locators
                        elif locator_type.lower() == 'id':
                            element = driver.find_element_by_id(locator_value)
                        elif locator_type.lower() == 'xpath':
                            element = driver.find_element_by_xpath(locator_value)
                        elif locator_type.lower() == 'name':
                            element = driver.find_element_by_name(locator_value)
                        elif locator_type.lower() == 'class':
                            element = driver.find_element_by_class_name(locator_value)
                        else:
                            # Try modern syntax if available
                            try:
                                from selenium.webdriver.common.by import By
                                # Map locator type to By enum
                                locator_map = {
                                    'id': By.ID,
                                    'xpath': By.XPATH,
                                    'name': By.NAME,
                                    'class': By.CLASS_NAME,
                                    'tag': By.TAG_NAME,
                                    'css': By.CSS_SELECTOR,
                                    'link_text': By.LINK_TEXT,
                                    'partial_link_text': By.PARTIAL_LINK_TEXT
                                }
                                by_type = locator_map.get(locator_type.lower())
                                if by_type:
                                    element = driver.find_element(by_type, locator_value)
                                else:
                                    # Try to use the locator type as-is
                                    element = driver.find_element(locator_type, locator_value)
                            except Exception as find_e:
                                self.logger.error(f"Error finding element: {find_e}")
                                return {
                                    "status": "error",
                                    "message": f"Element not found: {locator_type}='{locator_value}'. Error: {str(find_e)}"
                                }

                except Exception as e:
                    self.logger.error(f"Error finding element: {e}")
                    return {
                        "status": "error",
                        "message": f"Element not found: {locator_type}='{locator_value}'. Error: {str(e)}"
                    }

                # Input text into the element
                if element:
                    try:
                        # Check if we're on iOS platform
                        is_ios = hasattr(self.controller, 'platform_name') and self.controller.platform_name.lower() == 'ios'

                        if is_ios:
                            self.logger.info(f"Using iOS-specific text input methods for element with {locator_type}='{locator_value}'")

                            # Clear the field if requested
                            if clear_first:
                                try:
                                    self.logger.info("Clearing element before input")
                                    element.clear()
                                except Exception as clear_e:
                                    self.logger.warning(f"Could not clear element before input: {clear_e}")

                                    # Try alternative clearing methods for iOS
                                    try:
                                        self.logger.info("Trying alternative clearing method with backspace keys")
                                        element.send_keys("\ue003\ue003\ue003\ue003\ue003\ue003\ue003\ue003\ue003\ue003")  # Send multiple backspaces
                                    except Exception as alt_clear_e:
                                        self.logger.warning(f"Alternative clearing method failed: {alt_clear_e}")
                                        # Continue with input even if clear fails

                            # Method 1: Try using element.send_keys first (standard method)
                            try:
                                self.logger.info(f"Method 1: Using element.send_keys for iOS text input: '{text}'")
                                element.send_keys(text)
                                # Include both original and substituted text in the message if parameter substitution occurred
                                if text != original_text:
                                    return {
                                        "status": "success",
                                        "message": f"Text input successful: '{text}' (substituted from '{original_text}') into {locator_type}='{locator_value}'"
                                    }
                                else:
                                    return {
                                        "status": "success",
                                        "message": f"Text input successful using element.send_keys: '{text}' into {locator_type}='{locator_value}'"
                                    }
                            except Exception as ios_e1:
                                self.logger.warning(f"Method 1 failed - Error using element.send_keys: {ios_e1}")

                                # Method 2: Try clicking the element first, then using XCUITest's typeText
                                try:
                                    self.logger.info(f"Method 2: Clicking element then using XCUITest mobile:typeText for text input: '{text}'")
                                    # Click the element to focus it
                                    element.click()

                                    # Use the mobile: typeText command which is the most reliable for iOS
                                    driver.execute_script('mobile: typeText', {'text': text})

                                    return {
                                        "status": "success",
                                        "message": f"Text input successful using click+mobile:typeText: '{text}' into {locator_type}='{locator_value}'"
                                    }
                                except Exception as ios_e2:
                                    self.logger.warning(f"Method 2 failed - Error using click+mobile:typeText: {ios_e2}")

                                    # Method 3: Try clicking the element first, then using active element
                                    try:
                                        self.logger.info(f"Method 3: Clicking element then using active element for text input: '{text}'")
                                        # Click the element to focus it (try again)
                                        element.click()

                                        # Get the active element and send keys
                                        active_element = driver.switch_to.active_element
                                        active_element.send_keys(text)
                                        return {
                                            "status": "success",
                                            "message": f"Text input successful using click+active element: '{text}' into {locator_type}='{locator_value}'"
                                        }
                                    except Exception as ios_e3:
                                        self.logger.warning(f"Method 3 failed - Error using click+active element: {ios_e3}")

                                        # Method 4: Try using mobile: type (older method)
                                        try:
                                            self.logger.info(f"Method 4: Using iOS mobile: type for text input: '{text}'")
                                            # Use the older mobile: type command as a last resort
                                            driver.execute_script('mobile: type', {'text': text})
                                            return {
                                                "status": "success",
                                                "message": f"Text input successful using iOS mobile: type: '{text}' into {locator_type}='{locator_value}'"
                                            }
                                        except Exception as ios_e4:
                                            self.logger.warning(f"Method 4 failed - Error using iOS mobile: type: {ios_e4}")

                                            # Method 5: Try using mobile: typeText again with different syntax
                                            try:
                                                self.logger.info(f"Method 5: Using alternative mobile: typeText syntax for text input: '{text}'")
                                                # Try with different parameter format
                                                driver.execute_script('mobile: typeText', {'value': text})
                                                return {
                                                    "status": "success",
                                                    "message": f"Text input successful using alternative mobile: typeText: '{text}' into {locator_type}='{locator_value}'"
                                                }
                                            except Exception as ios_e5:
                                                self.logger.warning(f"Method 5 failed - Error using alternative mobile: typeText: {ios_e5}")
                                                # Fall back to error
                                            return {
                                                "status": "error",
                                                "message": f"All iOS text input methods failed for {locator_type}='{locator_value}'"
                                            }
                        else:
                            # Standard approach for non-iOS platforms
                            if clear_first:
                                try:
                                    element.clear()
                                except Exception as clear_e:
                                    self.logger.warning(f"Could not clear element before input: {clear_e}")
                                    # Continue with input even if clear fails

                            element.send_keys(text)
                            # Include both original and substituted text in the message if parameter substitution occurred
                            if text != original_text:
                                return {
                                    "status": "success",
                                    "message": f"Text input successful: '{text}' (substituted from '{original_text}') into {locator_type}='{locator_value}'"
                                }
                            else:
                                return {
                                    "status": "success",
                                    "message": f"Text input successful: '{text}' into {locator_type}='{locator_value}'"
                                }
                    except Exception as input_e:
                        self.logger.error(f"Error inputting text: {input_e}")
                        return {
                            "status": "error",
                            "message": f"Failed to input text: {input_e}"
                        }
                else:
                    return {
                        "status": "error",
                        "message": f"Element could not be located: {locator_type}='{locator_value}'"
                    }
            else:
                return {
                    "status": "error",
                    "message": "Device controller doesn't support text input operations"
                }

        except Exception as e:
            self.logger.error(f"Error executing input text action: {e}")
            return {"status": "error", "message": f"Input text action failed: {str(e)}"}

    def _has_selenium_support(self):
        """Check if Selenium WebDriverWait is available"""
        try:
            from selenium.webdriver.support.ui import WebDriverWait
            return True
        except ImportError:
            return False