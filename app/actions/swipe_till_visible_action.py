from base_action import BaseAction
import time
import os
import logging
from pathlib import Path
import sys

# Import text detection utility
try:
    import sys
    import os
    current_dir = os.path.dirname(os.path.abspath(__file__))
    parent_dir = os.path.dirname(current_dir)
    utils_dir = os.path.join(parent_dir, 'utils')
    if utils_dir not in sys.path:
        sys.path.insert(0, utils_dir)
    from text_detection import detect_text_in_image, scale_coordinates
except ImportError as e:
    import logging
    logging.getLogger(__name__).warning(f"Could not import text_detection: {e}")
    # Create dummy text_detection functions
    def detect_text_in_image(*args, **kwargs):
        return False, None
    def scale_coordinates(*args, **kwargs):
        return None

class SwipeTillVisibleAction(BaseAction):
    """Handler for swipe till visible actions"""
    
    def execute(self, params):
        """
        Execute swipe till visible action
        
        Args:
            params: Dictionary containing:
                - vector_start: Starting vector as percentage [x, y] (0-1 range)
                - vector_end: Ending vector as percentage [x, y] (0-1 range)
                - duration: Duration of swipe in milliseconds
                - count: Number of swipes to perform
                - interval: Time interval between swipes in seconds
                - locator_type: Type of locator (id, xpath, etc.)
                - locator_value: Value of the locator
                - image_filename: (Optional) Reference image to look for
                - threshold: (Optional) Similarity threshold for image matching
                - timeout: (Optional) Timeout for finding the element
                - text_to_find: (Optional) Text to find on screen
                
        Returns:
            dict: Result with status and message
        """
        if not self.controller:
            return {"status": "error", "message": "No device controller available"}
        
        # Get parameters with default values
        vector_start = params.get('vector_start', [0.5, 0.5])
        vector_end = params.get('vector_end', [0.5, 0.7])
        duration = params.get('duration', 300)  # Default 300ms
        count = int(params.get('count', 5))  # Default 5 swipes
        interval = float(params.get('interval', 0.5))  # Default 0.5s between swipes
        locator_type = params.get('locator_type')
        locator_value = params.get('locator_value')
        image_filename = params.get('image_filename')
        threshold = float(params.get('threshold', 0.7))  # Default threshold 0.7
        timeout = int(params.get('timeout', 20))  # Default timeout 20s
        text_to_find = params.get('text_to_find')
        
        # Log the parameters for debugging
        self.logger.info(f"SwipeTillVisible params - locator_type: '{locator_type}', locator_value: '{locator_value}', image_filename: '{image_filename}', text_to_find: '{text_to_find}'")
        
        try:
            # Ensure vectors are in 0-1 range (convert if they're percentages)
            if vector_start[0] > 1 or vector_start[1] > 1:
                self.logger.info(f"Converting vector_start from percentage to relative: {vector_start}")
                vector_start = [val/100 if val <= 100 else val for val in vector_start]
            
            if vector_end[0] > 1 or vector_end[1] > 1:
                self.logger.info(f"Converting vector_end from percentage to relative: {vector_end}")
                vector_end = [val/100 if val <= 100 else val for val in vector_end]
            
            # Get screen dimensions
            screen_size = self.controller.get_device_dimensions()
            if not screen_size:
                screen_size = (1080, 1920)  # Default fallback size
                self.logger.warning(f"Could not get device dimensions, using default: {screen_size}")
            
            # Convert relative coordinates to absolute
            start_x = int(vector_start[0] * screen_size[0])
            start_y = int(vector_start[1] * screen_size[1])
            end_x = int(vector_end[0] * screen_size[0])
            end_y = int(vector_end[1] * screen_size[1])
            
            self.logger.info(f"Swiping from ({start_x}, {start_y}) to ({end_x}, {end_y}), duration={duration}ms, count={count}, interval={interval}s")
            
            # Convert duration from ms to seconds for Airtest API
            duration_sec = duration / 1000.0
            
            # Perform swipes and check for element after each swipe
            for i in range(count):
                self.logger.info(f"Swipe {i+1}/{count}")
                
                # Check if element is already visible before swiping
                if i == 0 and self._is_element_visible(locator_type, locator_value, image_filename, text_to_find, threshold, timeout):
                    return {"status": "success", "message": f"Element already visible, no swipe needed"}
                
                # Execute the swipe
                if hasattr(self.controller, '_ensure_airtest_connected') and self.controller._ensure_airtest_connected():
                    # Using Airtest API directly
                    try:
                        import airtest.core.api as airtest_api
                        airtest_api.swipe(vector_start, vector_end, duration=duration_sec)
                        self.logger.info(f"Swiped from {vector_start} to {vector_end} using Airtest (swipe {i+1}/{count})")
                    except Exception as e:
                        self.logger.error(f"Airtest swipe failed: {e}, falling back to regular swipe")
                        result = self.controller.swipe(start_x, start_y, end_x, end_y, duration)
                else:
                    # Use regular device controller
                    result = self.controller.swipe(start_x, start_y, end_x, end_y, duration)
                
                # Wait for the specified interval
                time.sleep(interval)
                
                # Check if element is visible after swiping
                if self._is_element_visible(locator_type, locator_value, image_filename, text_to_find, threshold, timeout):
                    return {"status": "success", "message": f"Element visible after {i+1} swipe(s)"}
            
            # If we get here, element was not found after all swipes
            return {"status": "error", "message": f"Element not visible after {count} swipe(s)"}
                
        except Exception as e:
            self.logger.error(f"Error executing swipe till visible action: {e}")
            return {"status": "error", "message": f"Swipe till visible action failed: {str(e)}"}
    
    def _is_element_visible(self, locator_type, locator_value, image_filename=None, text_to_find=None, threshold=0.7, timeout=5):
        """
        Check if an element is visible on the screen
        
        Args:
            locator_type: Type of locator (id, xpath, etc.)
            locator_value: Value of the locator
            image_filename: (Optional) Reference image to look for
            text_to_find: (Optional) Text to find on screen
            threshold: Similarity threshold for image matching
            timeout: Timeout for finding the element
            
        Returns:
            bool: True if element is visible, False otherwise
        """
        try:
            # Priority 1: Check for image if provided
            if image_filename:
                self.logger.info(f"Checking for image: {image_filename}")
                try:
                    # Use Airtest's wait() function with a short timeout
                    from airtest.core.api import wait, Template, TargetNotFoundError
                    
                    # Resolve the image path
                    image_path = image_filename
                    
                    # Make sure image has a file extension
                    if not os.path.splitext(image_path)[1]:
                        self.logger.warning(f"Image parameter '{image_path}' has no file extension, trying to add .png")
                        image_path = f"{image_path}.png"
                    
                    # Try multiple paths to find the image
                    potential_paths = [
                        image_path,  # Direct path as provided
                        os.path.abspath(image_path),  # Absolute path
                        os.path.join('reference_images', image_path),  # In reference_images folder
                        os.path.join('app', 'reference_images', image_path),  # In app/reference_images folder
                    ]
                    
                    template = None
                    for path in potential_paths:
                        if os.path.exists(path):
                            self.logger.info(f"Found image at path: {path}")
                            template = Template(path, threshold=threshold)
                            break
                    
                    if not template:
                        self.logger.error(f"Could not find image at any of these paths: {potential_paths}")
                        return False
                    
                    # Try to find the image with a short timeout
                    try:
                        position = wait(template, timeout=timeout)
                        self.logger.info(f"Image found at position: {position}")
                        return True
                    except TargetNotFoundError:
                        self.logger.info(f"Image not found on screen")
                        return False
                    
                except Exception as e:
                    self.logger.error(f"Error checking for image: {e}")
                    return False
            
            # Priority 2: Check for text if provided
            elif text_to_find:
                self.logger.info(f"Checking for text: {text_to_find}")
                try:
                    # Take a screenshot
                    screenshot_path = self.controller.take_screenshot()
                    if not screenshot_path:
                        self.logger.error("Failed to take screenshot for text detection")
                        return False
                    
                    # Use text detection to find the text
                    text_found, text_coords = detect_text_in_image(screenshot_path, text_to_find)
                    if text_found:
                        self.logger.info(f"Text '{text_to_find}' found at {text_coords}")
                        return True
                    else:
                        self.logger.info(f"Text '{text_to_find}' not found on screen")
                        return False
                    
                except Exception as e:
                    self.logger.error(f"Error checking for text: {e}")
                    return False
            
            # Priority 3: Check for element using locator
            elif locator_type and locator_value:
                self.logger.info(f"Checking for element with {locator_type}: {locator_value}")
                element = self.controller.find_element(locator_type, locator_value, timeout=timeout)
                if element is not None:
                    self.logger.info(f"Element found")
                    return True
                else:
                    self.logger.info(f"Element not found")
                    return False
            
            # If no valid check method is provided
            else:
                self.logger.error("No valid check method provided (image, text, or locator)")
                return False
            
        except Exception as e:
            self.logger.error(f"Error in _is_element_visible: {e}")
            return False
