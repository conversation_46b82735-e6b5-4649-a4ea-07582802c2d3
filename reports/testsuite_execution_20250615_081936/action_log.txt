Action Log - 2025-06-15 08:21:04
================================================================================

[[08:21:04]] [INFO] Generating execution report...
[[08:21:04]] [WARNING] Execution stopped by user.
[[08:21:04]] [ERROR] Error executing action 10: Failed to fetch
[[08:21:01]] [WARNING] Stop requested. Finishing current action...
[[08:20:52]] [INFO] Executing action 10/59: Check if element with xpath="//XCUIElementTypeStaticText[@name="txtNo internet connection"]" exists
[[08:20:52]] [SUCCESS] Screenshot refreshed successfully
[[08:20:52]] [SUCCESS] Screenshot refreshed
[[08:20:51]] [INFO] Refreshing screenshot...
[[08:20:46]] [INFO] Executing action 9/59: Restart app: env[appid]
[[08:20:46]] [ERROR] Action 8 failed: Element not found or not tappable: xpath='//XCUIElementTypeSwitch[@name="Wi‑Fi"]'
[[08:20:32]] [INFO] Executing action 8/59: Tap on element with xpath: //XCUIElementTypeSwitch[@name="Wi‑Fi"]
[[08:20:32]] [ERROR] Action 7 failed: Unsupported action type: tapOnText
[[08:20:31]] [INFO] Executing action 7/59: Tap on Text: "Wi-Fi"
[[08:20:30]] [SUCCESS] Screenshot refreshed successfully
[[08:20:30]] [SUCCESS] Screenshot refreshed
[[08:20:30]] [INFO] Refreshing screenshot...
[[08:20:27]] [INFO] Executing action 6/59: Launch app: com.apple.Preferences
[[08:20:27]] [SUCCESS] Screenshot refreshed successfully
[[08:20:27]] [SUCCESS] Screenshot refreshed
[[08:20:26]] [INFO] Refreshing screenshot...
[[08:20:24]] [SUCCESS] Screenshot refreshed successfully
[[08:20:24]] [INFO] Executing action 5/59: Terminate app: com.apple.Preferences
[[08:20:24]] [SUCCESS] Screenshot refreshed
[[08:20:23]] [INFO] Refreshing screenshot...
[[08:20:22]] [SUCCESS] Screenshot refreshed successfully
[[08:20:22]] [SUCCESS] Screenshot refreshed
[[08:20:21]] [INFO] Refreshing screenshot...
[[08:20:18]] [INFO] Executing Multi Step action step 6/6: Check if element with xpath="//XCUIElementTypeStaticText[@name="txtHomeGreetingText"]" exists
[[08:20:17]] [SUCCESS] Screenshot refreshed successfully
[[08:20:17]] [SUCCESS] Screenshot refreshed
[[08:20:16]] [INFO] Refreshing screenshot...
[[08:20:12]] [INFO] Executing Multi Step action step 5/6: iOS Function: text
[[08:20:11]] [SUCCESS] Screenshot refreshed successfully
[[08:20:11]] [SUCCESS] Screenshot refreshed
[[08:20:11]] [INFO] Refreshing screenshot...
[[08:20:07]] [INFO] Executing Multi Step action step 4/6: Tap on element with xpath: //XCUIElementTypeSecureTextField[@name="Password"]
[[08:20:06]] [SUCCESS] Screenshot refreshed successfully
[[08:20:06]] [SUCCESS] Screenshot refreshed
[[08:20:05]] [INFO] Refreshing screenshot...
[[08:20:01]] [INFO] Executing Multi Step action step 3/6: iOS Function: text
[[08:20:01]] [SUCCESS] Screenshot refreshed successfully
[[08:20:00]] [SUCCESS] Screenshot refreshed
[[08:20:00]] [INFO] Refreshing screenshot...
[[08:19:56]] [INFO] Executing Multi Step action step 2/6: Tap on element with xpath: //XCUIElementTypeTextField[@name="Email"]
[[08:19:56]] [SUCCESS] Screenshot refreshed successfully
[[08:19:56]] [SUCCESS] Screenshot refreshed
[[08:19:55]] [INFO] Refreshing screenshot...
[[08:19:52]] [INFO] Executing Multi Step action step 1/6: Wait till xpath=//XCUIElementTypeTextField[@name="Email"]
[[08:19:52]] [INFO] Loaded 6 steps from test case: Kmart-Signin
[[08:19:52]] [INFO] Loading steps for Multi Step action: Kmart-Signin
[[08:19:52]] [INFO] Executing action 4/59: Execute Test Case: Kmart-Signin (6 steps)
[[08:19:51]] [SUCCESS] Screenshot refreshed successfully
[[08:19:51]] [SUCCESS] Screenshot refreshed
[[08:19:51]] [INFO] Refreshing screenshot...
[[08:19:48]] [INFO] Executing action 3/59: iOS Function: alert_accept
[[08:19:47]] [SUCCESS] Screenshot refreshed successfully
[[08:19:47]] [SUCCESS] Screenshot refreshed
[[08:19:46]] [INFO] Refreshing screenshot...
[[08:19:43]] [INFO] Executing action 2/59: Tap on element with xpath: //XCUIElementTypeButton[@name="txtHomeAccountCtaSignIn"]
[[08:19:42]] [SUCCESS] Screenshot refreshed successfully
[[08:19:42]] [SUCCESS] Screenshot refreshed
[[08:19:41]] [INFO] Refreshing screenshot...
[[08:19:36]] [INFO] Executing action 1/59: Restart app: env[appid]
[[08:19:36]] [INFO] ExecutionManager: Starting execution of 59 actions...
[[08:19:36]] [WARNING] Error clearing screenshots: Unexpected token '<', "
        
[[08:19:36]] [INFO] Clearing screenshots from database before execution...
[[08:19:36]] [SUCCESS] All screenshots deleted successfully
[[08:19:36]] [INFO] Deleting all screenshots in app/static/screenshots folder...
[[08:19:36]] [INFO] Screenshots directory: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/reports/testsuite_execution_20250615_081936/screenshots
[[08:19:36]] [INFO] Report directory: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/reports/testsuite_execution_20250615_081936
[[08:19:36]] [SUCCESS] Report directory initialized successfully
[[08:19:36]] [INFO] Initializing report directory and screenshots folder...
[[08:19:34]] [SUCCESS] All screenshots deleted successfully
[[08:19:34]] [SUCCESS] Loaded test case "App Settings AU" with 59 actions
[[08:19:34]] [SUCCESS] Added action: tap
[[08:19:34]] [SUCCESS] Added action: tap
[[08:19:34]] [SUCCESS] Added action: restartApp
[[08:19:34]] [SUCCESS] Added action: tap
[[08:19:34]] [SUCCESS] Added action: tap
[[08:19:34]] [SUCCESS] Added action: swipeTillVisible
[[08:19:34]] [SUCCESS] Added action: tap
[[08:19:34]] [SUCCESS] Added action: tap
[[08:19:34]] [SUCCESS] Added action: tap
[[08:19:34]] [SUCCESS] Added action: swipeTillVisible
[[08:19:34]] [SUCCESS] Added action: tap
[[08:19:34]] [SUCCESS] Added action: tap
[[08:19:34]] [SUCCESS] Added action: tap
[[08:19:34]] [SUCCESS] Added action: tap
[[08:19:34]] [SUCCESS] Added action: tap
[[08:19:34]] [SUCCESS] Added action: tap
[[08:19:34]] [SUCCESS] Added action: tap
[[08:19:34]] [SUCCESS] Added action: tap
[[08:19:34]] [SUCCESS] Added action: tapOnText
[[08:19:34]] [SUCCESS] Added action: tap
[[08:19:34]] [SUCCESS] Added action: ifElseSteps
[[08:19:34]] [SUCCESS] Added action: tapOnText
[[08:19:34]] [SUCCESS] Added action: tap
[[08:19:34]] [SUCCESS] Added action: tap
[[08:19:34]] [SUCCESS] Added action: tap
[[08:19:34]] [SUCCESS] Added action: tapOnText
[[08:19:34]] [SUCCESS] Added action: tap
[[08:19:34]] [SUCCESS] Added action: ifElseSteps
[[08:19:34]] [SUCCESS] Added action: tapOnText
[[08:19:34]] [SUCCESS] Added action: tap
[[08:19:34]] [SUCCESS] Added action: exists
[[08:19:34]] [SUCCESS] Added action: tap
[[08:19:34]] [SUCCESS] Added action: swipe
[[08:19:34]] [SUCCESS] Added action: iosFunctions
[[08:19:34]] [SUCCESS] Added action: tap
[[08:19:34]] [SUCCESS] Added action: restartApp
[[08:19:34]] [SUCCESS] Added action: tapOnText
[[08:19:34]] [SUCCESS] Added action: swipe
[[08:19:34]] [SUCCESS] Added action: tap
[[08:19:34]] [SUCCESS] Added action: restartApp
[[08:19:34]] [SUCCESS] Added action: wait
[[08:19:34]] [SUCCESS] Added action: tap
[[08:19:34]] [SUCCESS] Added action: launchApp
[[08:19:34]] [SUCCESS] Added action: exists
[[08:19:34]] [SUCCESS] Added action: tap
[[08:19:34]] [SUCCESS] Added action: exists
[[08:19:34]] [SUCCESS] Added action: tap
[[08:19:34]] [SUCCESS] Added action: exists
[[08:19:34]] [SUCCESS] Added action: tap
[[08:19:34]] [SUCCESS] Added action: exists
[[08:19:34]] [SUCCESS] Added action: restartApp
[[08:19:34]] [SUCCESS] Added action: tap
[[08:19:34]] [SUCCESS] Added action: tapOnText
[[08:19:34]] [SUCCESS] Added action: launchApp
[[08:19:34]] [SUCCESS] Added action: terminateApp
[[08:19:34]] [SUCCESS] Added action: multiStep
[[08:19:34]] [SUCCESS] Added action: iosFunctions
[[08:19:34]] [SUCCESS] Added action: tap
[[08:19:34]] [SUCCESS] Added action: restartApp
[[08:19:34]] [INFO] All actions cleared
[[08:19:34]] [INFO] Cleaning up screenshots...
[[08:19:31]] [SUCCESS] Connected to device: 00008120-00186C801E13C01E with AirTest support
[[08:19:31]] [INFO] Device info updated: 00008120-00186C801E13C01E
[[08:19:25]] [INFO] Connecting to device: 00008120-00186C801E13C01E (Platform: iOS)...
[[08:19:21]] [SUCCESS] Found 1 device(s)
[[08:19:20]] [INFO] Refreshing device list...
