Action Log - 2025-06-15 08:17:22
================================================================================

[[08:17:22]] [INFO] Generating execution report...
[[08:17:22]] [WARNING] Execution stopped by user.
[[08:17:21]] [SUCCESS] Screenshot refreshed successfully
[[08:17:21]] [SUCCESS] Screenshot refreshed
[[08:17:20]] [WARNING] Stop requested. Finishing current action...
[[08:17:20]] [INFO] Refreshing screenshot...
[[08:17:18]] [INFO] Executing action 3/59: iOS Function: alert_accept
[[08:17:18]] [ERROR] Action 2 failed: Element not found or not tappable: xpath='//XCUIElementTypeButton[@name="txtHomeAccountCtaSignIn"]'
[[08:17:04]] [INFO] Executing action 2/59: Tap on element with xpath: //XCUIElementTypeButton[@name="txtHomeAccountCtaSignIn"]
[[08:17:04]] [ERROR] Action 1 failed: Failed to launch app: Failed to launch app: Message: Error Domain=FBSOpenApplicationServiceErrorDomain Code=4 "The request to open "env[appid]" failed." UserInfo={BSErrorCodeDescription=InvalidRequest, NSLocalizedDescription=The request to open "env[appid]" failed., FBSOpenApplicationRequestID=0xd92f, NSUnderlyingError=0x12f67d3e0 {Error Domain=FBSOpenApplicationErrorDomain Code=4 "Application info provider (FBSApplicationLibrary) returned nil for "env[appid]"" UserInfo={BSErrorCodeDescription=NotFound, NSLocalizedFailureReason=Application info provider (FBSApplicationLibrary) returned nil for "env[appid]"}}}
Stacktrace:
InvalidArgumentError: Error Domain=FBSOpenApplicationServiceErrorDomain Code=4 "The request to open "env[appid]" failed." UserInfo={BSErrorCodeDescription=InvalidRequest, NSLocalizedDescription=The request to open "env[appid]" failed., FBSOpenApplicationRequestID=0xd92f, NSUnderlyingError=0x12f67d3e0 {Error Domain=FBSOpenApplicationErrorDomain Code=4 "Application info provider (FBSApplicationLibrary) returned nil for "env[appid]"" UserInfo={BSErrorCodeDescription=NotFound, NSLocalizedFailureReason=Application info provider (FBSApplicationLibrary) returned nil for "env[appid]"}}}
    at errorFromW3CJsonCode (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/appium-xcuitest-driver/node_modules/@appium/base-driver/lib/protocol/errors.js:1110:25)
    at ProxyRequestError.getActualError (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/appium-xcuitest-driver/node_modules/@appium/base-driver/lib/protocol/errors.js:979:14)
    at JWProxy.command (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/appium-xcuitest-driver/node_modules/@appium/base-driver/lib/jsonwp-proxy/proxy.js:357:19)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at XCUITestDriver.proxyCommand (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/appium-xcuitest-driver/lib/commands/proxy-helper.js:100:35)
    at XCUITestDriver.mobileLaunchApp (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/appium-xcuitest-driver/lib/commands/app-management.js:122:5)
    at XCUITestDriver.executeMethod (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/@appium/base-driver/lib/basedriver/commands/execute.ts:61:12)
    at XCUITestDriver.execute (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/appium-xcuitest-driver/lib/commands/execute.js:117:14)
[[08:17:00]] [INFO] Executing action 1/59: Restart app: env[appid]
[[08:17:00]] [INFO] ExecutionManager: Starting execution of 59 actions...
[[08:17:00]] [WARNING] Error clearing screenshots: Unexpected token '<', "
        
[[08:17:00]] [INFO] Clearing screenshots from database before execution...
[[08:17:00]] [SUCCESS] All screenshots deleted successfully
[[08:17:00]] [INFO] Deleting all screenshots in app/static/screenshots folder...
[[08:17:00]] [INFO] Screenshots directory: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/reports/testsuite_execution_20250615_081700/screenshots
[[08:17:00]] [INFO] Report directory: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/reports/testsuite_execution_20250615_081700
[[08:17:00]] [SUCCESS] Report directory initialized successfully
[[08:17:00]] [INFO] Initializing report directory and screenshots folder...
[[08:16:44]] [SUCCESS] All screenshots deleted successfully
[[08:16:44]] [SUCCESS] Loaded test case "App Settings AU" with 59 actions
[[08:16:44]] [SUCCESS] Added action: tap
[[08:16:44]] [SUCCESS] Added action: tap
[[08:16:44]] [SUCCESS] Added action: restartApp
[[08:16:44]] [SUCCESS] Added action: tap
[[08:16:44]] [SUCCESS] Added action: tap
[[08:16:44]] [SUCCESS] Added action: swipeTillVisible
[[08:16:44]] [SUCCESS] Added action: tap
[[08:16:44]] [SUCCESS] Added action: tap
[[08:16:44]] [SUCCESS] Added action: tap
[[08:16:44]] [SUCCESS] Added action: swipeTillVisible
[[08:16:44]] [SUCCESS] Added action: tap
[[08:16:44]] [SUCCESS] Added action: tap
[[08:16:44]] [SUCCESS] Added action: tap
[[08:16:44]] [SUCCESS] Added action: tap
[[08:16:44]] [SUCCESS] Added action: tap
[[08:16:44]] [SUCCESS] Added action: tap
[[08:16:44]] [SUCCESS] Added action: tap
[[08:16:44]] [SUCCESS] Added action: tap
[[08:16:44]] [SUCCESS] Added action: tapOnText
[[08:16:44]] [SUCCESS] Added action: tap
[[08:16:44]] [SUCCESS] Added action: ifElseSteps
[[08:16:44]] [SUCCESS] Added action: tapOnText
[[08:16:44]] [SUCCESS] Added action: tap
[[08:16:44]] [SUCCESS] Added action: tap
[[08:16:44]] [SUCCESS] Added action: tap
[[08:16:44]] [SUCCESS] Added action: tapOnText
[[08:16:44]] [SUCCESS] Added action: tap
[[08:16:44]] [SUCCESS] Added action: ifElseSteps
[[08:16:44]] [SUCCESS] Added action: tapOnText
[[08:16:44]] [SUCCESS] Added action: tap
[[08:16:44]] [SUCCESS] Added action: exists
[[08:16:44]] [SUCCESS] Added action: tap
[[08:16:44]] [SUCCESS] Added action: swipe
[[08:16:44]] [SUCCESS] Added action: iosFunctions
[[08:16:44]] [SUCCESS] Added action: tap
[[08:16:44]] [SUCCESS] Added action: restartApp
[[08:16:44]] [SUCCESS] Added action: tapOnText
[[08:16:44]] [SUCCESS] Added action: swipe
[[08:16:44]] [SUCCESS] Added action: tap
[[08:16:44]] [SUCCESS] Added action: restartApp
[[08:16:44]] [SUCCESS] Added action: wait
[[08:16:44]] [SUCCESS] Added action: tap
[[08:16:44]] [SUCCESS] Added action: launchApp
[[08:16:44]] [SUCCESS] Added action: exists
[[08:16:44]] [SUCCESS] Added action: tap
[[08:16:44]] [SUCCESS] Added action: exists
[[08:16:44]] [SUCCESS] Added action: tap
[[08:16:44]] [SUCCESS] Added action: exists
[[08:16:44]] [SUCCESS] Added action: tap
[[08:16:44]] [SUCCESS] Added action: exists
[[08:16:44]] [SUCCESS] Added action: restartApp
[[08:16:44]] [SUCCESS] Added action: tap
[[08:16:44]] [SUCCESS] Added action: tapOnText
[[08:16:44]] [SUCCESS] Added action: launchApp
[[08:16:44]] [SUCCESS] Added action: terminateApp
[[08:16:44]] [SUCCESS] Added action: multiStep
[[08:16:44]] [SUCCESS] Added action: iosFunctions
[[08:16:44]] [SUCCESS] Added action: tap
[[08:16:44]] [SUCCESS] Added action: restartApp
[[08:16:44]] [INFO] All actions cleared
[[08:16:44]] [INFO] Cleaning up screenshots...
[[08:16:40]] [SUCCESS] Connected to device: 00008120-00186C801E13C01E with AirTest support
[[08:16:40]] [INFO] Device info updated: 00008120-00186C801E13C01E
[[08:16:33]] [INFO] Connecting to device: 00008120-00186C801E13C01E (Platform: iOS)...
[[08:16:30]] [SUCCESS] Found 1 device(s)
[[08:16:29]] [INFO] Refreshing device list...
