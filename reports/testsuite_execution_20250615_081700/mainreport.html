<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Suite Report - 6/15/2025, 8:17:22 AM</title>
    <link rel="stylesheet" href="./assets/report.css">
    <link rel="stylesheet" href="./assets/custom.css">
</head>
<body>
    <header class="header">
        <h1>Suites</h1>
        <div class="status-summary">
            Status: <span class="status-badge status-badge-passed">passed</span>
            <span class="stats-summary">
                <span class="passed-count">1</span> passed,
                <span class="failed-count">0</span> failed,
                <span class="skipped-count">0</span> skipped
            </span>
        </div>
    </header>

    <div class="content">
        <div class="suites-panel">
            <div class="suite-heading">
                <span class="expand-icon"></span>
                UI Execution 15/06/2025, 08:17:22
            </div>

            <ul class="test-list">
                <li class="test-item">
                    <div class="test-header">
                        <div class="test-case" data-actions="59 actions">
                            <span class="expand-icon"></span>
                            <span class="status-icon status-icon-passed"></span>
                            #1 Test Case
                        </div>
                        <span class="test-duration">0ms</span>
                    </div>
                    <ul class="test-steps">
                        <li class="test-step" data-step-id="1" data-status="failed"
                            data-screenshot="XEbZHdi0GT.png" data-action-id="XEbZHdi0GT" onclick="showStepDetails('step-0-0')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-failed"></span>
                                Restart app: env[appid] <span class="action-id-badge" title="Action ID: XEbZHdi0GT">XEbZHdi0GT</span>
                            </div>
                            <span class="test-step-duration">2206ms</span>
                        </li>
                        <li class="test-step" data-step-id="2" data-status="failed"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-0-1')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-failed"></span>
                                Tap on element with xpath: //XCUIElementTypeButton[@name="txtHomeAccountCtaSignIn"] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">2410ms</span>
                        </li>
                        <li class="test-step" data-step-id="3" data-status="passed"
                            data-screenshot="veukWo4573.png" data-action-id="veukWo4573" onclick="showStepDetails('step-0-2')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                iOS Function: alert_accept <span class="action-id-badge" title="Action ID: veukWo4573">veukWo4573</span>
                            </div>
                            <span class="test-step-duration">209ms</span>
                        </li>
                        <li class="test-step" data-step-id="4" data-status="unknown"
                            data-screenshot="rJ86z4njuR.png" data-action-id="rJ86z4njuR" onclick="showStepDetails('step-0-3')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Execute Test Case: Kmart-Signin (6 steps) <span class="action-id-badge" title="Action ID: rJ86z4njuR">rJ86z4njuR</span>
                            </div>
                            <span class="test-step-duration">0ms</span>
                        </li>
                        <li class="test-step" data-step-id="5" data-status="unknown"
                            data-screenshot="Preference.png" data-action-id="Preference" onclick="showStepDetails('step-0-4')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Terminate app: com.apple.Preferences <span class="action-id-badge" title="Action ID: Preference">Preference</span>
                            </div>
                            <span class="test-step-duration">32ms</span>
                        </li>
                        <li class="test-step" data-step-id="6" data-status="unknown"
                            data-screenshot="Preference.png" data-action-id="Preference" onclick="showStepDetails('step-0-5')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Launch app: com.apple.Preferences <span class="action-id-badge" title="Action ID: Preference">Preference</span>
                            </div>
                            <span class="test-step-duration">1207ms</span>
                        </li>
                        <li class="test-step" data-step-id="7" data-status="unknown"
                            data-screenshot="placeholder_report.png" data-action-id="placeholder_report" onclick="showStepDetails('step-0-6')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Tap on Text: "Wi-Fi" <span class="action-id-badge" title="Action ID: placeholder_report">placeholder_report</span>
                            </div>
                            <span class="test-step-duration">3310ms</span>
                        </li>
                        <li class="test-step" data-step-id="8" data-status="unknown"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-0-7')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Tap on element with xpath: //XCUIElementTypeSwitch[@name="Wi‑Fi"] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">1034ms</span>
                        </li>
                        <li class="test-step" data-step-id="9" data-status="unknown"
                            data-screenshot="placeholder_report.png" data-action-id="placeholder_report" onclick="showStepDetails('step-0-8')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Restart app: env[appid] <span class="action-id-badge" title="Action ID: placeholder_report">placeholder_report</span>
                            </div>
                            <span class="test-step-duration">3220ms</span>
                        </li>
                        <li class="test-step" data-step-id="10" data-status="unknown"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-0-9')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Check if element with xpath="//XCUIElementTypeStaticText[@name="txtNo internet connection"]" exists <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">235ms</span>
                        </li>
                        <li class="test-step" data-step-id="11" data-status="unknown"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-0-10')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"2 of 5")] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">724ms</span>
                        </li>
                        <li class="test-step" data-step-id="12" data-status="unknown"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-0-11')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Check if element with xpath="//XCUIElementTypeStaticText[@name="txtNo internet connection"]" exists <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">207ms</span>
                        </li>
                        <li class="test-step" data-step-id="13" data-status="unknown"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-0-12')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"3 of 5")] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">652ms</span>
                        </li>
                        <li class="test-step" data-step-id="14" data-status="unknown"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-0-13')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Check if element with xpath="//XCUIElementTypeStaticText[@name="txtNo internet connection"]" exists <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">214ms</span>
                        </li>
                        <li class="test-step" data-step-id="15" data-status="unknown"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-0-14')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"4 of 5")] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">681ms</span>
                        </li>
                        <li class="test-step" data-step-id="16" data-status="unknown"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-0-15')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Check if element with xpath="//XCUIElementTypeStaticText[@name="txtNo internet connection"]" exists <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">176ms</span>
                        </li>
                        <li class="test-step" data-step-id="17" data-status="unknown"
                            data-screenshot="Preference.png" data-action-id="Preference" onclick="showStepDetails('step-0-16')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Launch app: com.apple.Preferences <span class="action-id-badge" title="Action ID: Preference">Preference</span>
                            </div>
                            <span class="test-step-duration">125ms</span>
                        </li>
                        <li class="test-step" data-step-id="18" data-status="unknown"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-0-17')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Tap on element with xpath: //XCUIElementTypeSwitch[@name="Wi‑Fi"] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">762ms</span>
                        </li>
                        <li class="test-step" data-step-id="19" data-status="unknown"
                            data-screenshot="placeholder_report.png" data-action-id="placeholder_report" onclick="showStepDetails('step-0-18')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Wait for 5 ms <span class="action-id-badge" title="Action ID: placeholder_report">placeholder_report</span>
                            </div>
                            <span class="test-step-duration">5016ms</span>
                        </li>
                        <li class="test-step" data-step-id="20" data-status="unknown"
                            data-screenshot="placeholder_report.png" data-action-id="placeholder_report" onclick="showStepDetails('step-0-19')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Restart app: env[appid] <span class="action-id-badge" title="Action ID: placeholder_report">placeholder_report</span>
                            </div>
                            <span class="test-step-duration">3213ms</span>
                        </li>
                        <li class="test-step" data-step-id="21" data-status="unknown"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-0-20')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"5 of 5")] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">681ms</span>
                        </li>
                        <li class="test-step" data-step-id="22" data-status="unknown"
                            data-screenshot="placeholder_report.png" data-action-id="placeholder_report" onclick="showStepDetails('step-0-21')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Swipe from (50%, 70%) to (50%, 30%) <span class="action-id-badge" title="Action ID: placeholder_report">placeholder_report</span>
                            </div>
                            <span class="test-step-duration">0ms</span>
                        </li>
                        <li class="test-step" data-step-id="23" data-status="unknown"
                            data-screenshot="placeholder_report.png" data-action-id="placeholder_report" onclick="showStepDetails('step-0-22')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Tap on Text: "out" <span class="action-id-badge" title="Action ID: placeholder_report">placeholder_report</span>
                            </div>
                            <span class="test-step-duration">0ms</span>
                        </li>
                        <li class="test-step" data-step-id="24" data-status="unknown"
                            data-screenshot="mobilesafa.png" data-action-id="mobilesafa" onclick="showStepDetails('step-0-23')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Restart app: com.apple.mobilesafari <span class="action-id-badge" title="Action ID: mobilesafa">mobilesafa</span>
                            </div>
                            <span class="test-step-duration">0ms</span>
                        </li>
                        <li class="test-step" data-step-id="25" data-status="unknown"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-0-24')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Tap on element with xpath: //XCUIElementTypeTextField[@name="TabBarItemTitle"] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">0ms</span>
                        </li>
                        <li class="test-step" data-step-id="26" data-status="unknown"
                            data-screenshot="placeholder_report.png" data-action-id="placeholder_report" onclick="showStepDetails('step-0-25')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                iOS Function: text <span class="action-id-badge" title="Action ID: placeholder_report">placeholder_report</span>
                            </div>
                            <span class="test-step-duration">0ms</span>
                        </li>
                        <li class="test-step" data-step-id="27" data-status="unknown"
                            data-screenshot="placeholder_report.png" data-action-id="placeholder_report" onclick="showStepDetails('step-0-26')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Swipe from (50%, 70%) to (50%, 30%) <span class="action-id-badge" title="Action ID: placeholder_report">placeholder_report</span>
                            </div>
                            <span class="test-step-duration">0ms</span>
                        </li>
                        <li class="test-step" data-step-id="28" data-status="unknown"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-0-27')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Tap on element with xpath: //XCUIElementTypeStaticText[@name="https://www.kmart.com.au"] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">0ms</span>
                        </li>
                        <li class="test-step" data-step-id="29" data-status="unknown"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-0-28')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Check if element with xpath="//XCUIElementTypeButton[@name="txtHomeAccountCtaSignIn"]" exists <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">0ms</span>
                        </li>
                        <li class="test-step" data-step-id="30" data-status="unknown"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-0-29')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"2 of 5")] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">681ms</span>
                        </li>
                        <li class="test-step" data-step-id="31" data-status="unknown"
                            data-screenshot="placeholder_report.png" data-action-id="placeholder_report" onclick="showStepDetails('step-0-30')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Tap on Text: "Catalogue" <span class="action-id-badge" title="Action ID: placeholder_report">placeholder_report</span>
                            </div>
                            <span class="test-step-duration">0ms</span>
                        </li>
                        <li class="test-step" data-step-id="32" data-status="unknown"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-0-31')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                If exists: xpath="//XCUIElementTypeOther[@name="Kmart Catalogue"]/XCUIElementTypeImage[1]" (timeout: 20s) → Then click element: xpath="//XCUIElementTypeOther[@name="Kmart Catalogue"]/XCUIElementTypeImage[1]" <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">7212ms</span>
                        </li>
                        <li class="test-step" data-step-id="33" data-status="unknown"
                            data-screenshot="placeholder_report.png" data-action-id="placeholder_report" onclick="showStepDetails('step-0-32')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Tap on image: env[catalogue-menu-img] <span class="action-id-badge" title="Action ID: placeholder_report">placeholder_report</span>
                            </div>
                            <span class="test-step-duration">0ms</span>
                        </li>
                        <li class="test-step" data-step-id="34" data-status="unknown"
                            data-screenshot="placeholder_report.png" data-action-id="placeholder_report" onclick="showStepDetails('step-0-33')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Tap on Text: "List" <span class="action-id-badge" title="Action ID: placeholder_report">placeholder_report</span>
                            </div>
                            <span class="test-step-duration">0ms</span>
                        </li>
                        <li class="test-step" data-step-id="35" data-status="unknown"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-0-34')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Tap on element with xpath: (//XCUIElementTypeOther[@name="Kmart Catalogue"]//XCUIElementTypeStaticText[contains(@name,"$")])[1] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">0ms</span>
                        </li>
                        <li class="test-step" data-step-id="36" data-status="unknown"
                            data-screenshot="accessibil.png" data-action-id="accessibil" onclick="showStepDetails('step-0-35')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Tap on element with accessibility_id: Add to bag <span class="action-id-badge" title="Action ID: accessibil">accessibil</span>
                            </div>
                            <span class="test-step-duration">0ms</span>
                        </li>
                        <li class="test-step" data-step-id="37" data-status="unknown"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-0-36')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"5 of 5")] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">681ms</span>
                        </li>
                        <li class="test-step" data-step-id="38" data-status="unknown"
                            data-screenshot="placeholder_report.png" data-action-id="placeholder_report" onclick="showStepDetails('step-0-37')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Tap on Text: "Catalogue" <span class="action-id-badge" title="Action ID: placeholder_report">placeholder_report</span>
                            </div>
                            <span class="test-step-duration">0ms</span>
                        </li>
                        <li class="test-step" data-step-id="39" data-status="unknown"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-0-38')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                If exists: xpath="//XCUIElementTypeOther[@name="Kmart Catalogue"]/XCUIElementTypeImage[1]" (timeout: 20s) → Then click element: xpath="//XCUIElementTypeOther[@name="Kmart Catalogue"]/XCUIElementTypeImage[1]" <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">7212ms</span>
                        </li>
                        <li class="test-step" data-step-id="40" data-status="unknown"
                            data-screenshot="placeholder_report.png" data-action-id="placeholder_report" onclick="showStepDetails('step-0-39')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Tap on image: env[catalogue-menu-img] <span class="action-id-badge" title="Action ID: placeholder_report">placeholder_report</span>
                            </div>
                            <span class="test-step-duration">0ms</span>
                        </li>
                        <li class="test-step" data-step-id="41" data-status="unknown"
                            data-screenshot="placeholder_report.png" data-action-id="placeholder_report" onclick="showStepDetails('step-0-40')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Tap on Text: "List" <span class="action-id-badge" title="Action ID: placeholder_report">placeholder_report</span>
                            </div>
                            <span class="test-step-duration">0ms</span>
                        </li>
                        <li class="test-step" data-step-id="42" data-status="unknown"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-0-41')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Tap on element with xpath: (//XCUIElementTypeOther[@name="Kmart Catalogue"]//XCUIElementTypeStaticText[contains(@name,"$")])[1] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">0ms</span>
                        </li>
                        <li class="test-step" data-step-id="43" data-status="unknown"
                            data-screenshot="accessibil.png" data-action-id="accessibil" onclick="showStepDetails('step-0-42')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Tap on element with accessibility_id: Add to bag <span class="action-id-badge" title="Action ID: accessibil">accessibil</span>
                            </div>
                            <span class="test-step-duration">0ms</span>
                        </li>
                        <li class="test-step" data-step-id="44" data-status="unknown"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-0-43')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"4 of 5")] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">681ms</span>
                        </li>
                        <li class="test-step" data-step-id="45" data-status="unknown"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-0-44')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Tap on element with xpath: //XCUIElementTypeButton[@name="Increase quantity"] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">0ms</span>
                        </li>
                        <li class="test-step" data-step-id="46" data-status="unknown"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-0-45')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Tap on element with xpath: //XCUIElementTypeButton[@name="Decrease quantity"] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">0ms</span>
                        </li>
                        <li class="test-step" data-step-id="47" data-status="unknown"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-0-46')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Remove")] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">0ms</span>
                        </li>
                        <li class="test-step" data-step-id="48" data-status="unknown"
                            data-screenshot="placeholder_report.png" data-action-id="placeholder_report" onclick="showStepDetails('step-0-47')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Tap on image: banner-close-updated.png <span class="action-id-badge" title="Action ID: placeholder_report">placeholder_report</span>
                            </div>
                            <span class="test-step-duration">0ms</span>
                        </li>
                        <li class="test-step" data-step-id="49" data-status="unknown"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-0-48')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"1 of 5")] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">724ms</span>
                        </li>
                        <li class="test-step" data-step-id="50" data-status="unknown"
                            data-screenshot="swipeTillV.png" data-action-id="swipeTillV" onclick="showStepDetails('step-0-49')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                swipeTillVisible action <span class="action-id-badge" title="Action ID: swipeTillV">swipeTillV</span>
                            </div>
                            <span class="test-step-duration">0ms</span>
                        </li>
                        <li class="test-step" data-step-id="51" data-status="unknown"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-0-50')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Tap on element with xpath: //XCUIElementTypeButton[@name="Home & Living"] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">0ms</span>
                        </li>
                        <li class="test-step" data-step-id="52" data-status="unknown"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-0-51')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Tap on element with xpath: (//XCUIElementTypeScrollView)[4]/following-sibling::XCUIElementTypeOther[1] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">0ms</span>
                        </li>
                        <li class="test-step" data-step-id="53" data-status="unknown"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-0-52')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Tap on element with xpath: (//XCUIElementTypeScrollView/following-sibling::XCUIElementTypeImage[contains(@name,"$")])[1] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">0ms</span>
                        </li>
                        <li class="test-step" data-step-id="54" data-status="unknown"
                            data-screenshot="swipeTillV.png" data-action-id="swipeTillV" onclick="showStepDetails('step-0-53')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                swipeTillVisible action <span class="action-id-badge" title="Action ID: swipeTillV">swipeTillV</span>
                            </div>
                            <span class="test-step-duration">0ms</span>
                        </li>
                        <li class="test-step" data-step-id="55" data-status="unknown"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-0-54')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Tap on element with xpath: (//XCUIElementTypeOther[@name="You may also like"]/following-sibling::*[1]//XCUIElementTypeLink)[1] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">0ms</span>
                        </li>
                        <li class="test-step" data-step-id="56" data-status="unknown"
                            data-screenshot="accessibil.png" data-action-id="accessibil" onclick="showStepDetails('step-0-55')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Tap on element with accessibility_id: Add to bag <span class="action-id-badge" title="Action ID: accessibil">accessibil</span>
                            </div>
                            <span class="test-step-duration">0ms</span>
                        </li>
                        <li class="test-step" data-step-id="57" data-status="unknown"
                            data-screenshot="placeholder_report.png" data-action-id="placeholder_report" onclick="showStepDetails('step-0-56')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Restart app: env[appid] <span class="action-id-badge" title="Action ID: placeholder_report">placeholder_report</span>
                            </div>
                            <span class="test-step-duration">0ms</span>
                        </li>
                        <li class="test-step" data-step-id="58" data-status="unknown"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-0-57')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"4 of 5")] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">681ms</span>
                        </li>
                        <li class="test-step" data-step-id="59" data-status="unknown"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-0-58')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Remove")] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">0ms</span>
                        </li>
                    </ul>
                </li>
            </ul>
        </div>

        <div class="details-panel" id="details-panel">
            <!-- This will be populated by JavaScript when a test step is clicked -->
            <h3>Click on a test step to view details</h3>
        </div>
    </div>

    <script>
        // Store the test data for our JavaScript to use
        const testData = {"name":"UI Execution 15/06/2025, 08:17:22","testCases":[{"name":"Test Case","status":"passed","steps":[{"name":"Restart app: env[appid]","status":"failed","duration":"2206ms","action_id":"XEbZHdi0GT","screenshot_filename":"XEbZHdi0GT.png","report_screenshot":"XEbZHdi0GT.png","resolved_screenshot":"screenshots/XEbZHdi0GT.png","clean_action_id":"XEbZHdi0GT","prefixed_action_id":"al_XEbZHdi0GT","action_id_screenshot":"screenshots/XEbZHdi0GT.png"},{"name":"Tap on element with xpath: //XCUIElementTypeButton[@name=\"txtHomeAccountCtaSignIn\"]","status":"failed","duration":"2410ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"iOS Function: alert_accept","status":"passed","duration":"209ms","action_id":"veukWo4573","screenshot_filename":"veukWo4573.png","report_screenshot":"veukWo4573.png","resolved_screenshot":"screenshots/veukWo4573.png","clean_action_id":"veukWo4573","prefixed_action_id":"al_veukWo4573","action_id_screenshot":"screenshots/veukWo4573.png"},{"name":"Execute Test Case: Kmart-Signin (6 steps)","status":"unknown","duration":"0ms","action_id":"rJ86z4njuR","screenshot_filename":"rJ86z4njuR.png","report_screenshot":"rJ86z4njuR.png","resolved_screenshot":"screenshots/rJ86z4njuR.png","clean_action_id":"rJ86z4njuR","prefixed_action_id":"al_rJ86z4njuR","action_id_screenshot":"screenshots/rJ86z4njuR.png"},{"name":"Terminate app: com.apple.Preferences","status":"unknown","duration":"32ms","action_id":"Preference","screenshot_filename":"Preference.png","report_screenshot":"Preference.png","resolved_screenshot":"screenshots/Preference.png","action_id_screenshot":"screenshots/Preference.png"},{"name":"Launch app: com.apple.Preferences","status":"unknown","duration":"1207ms","action_id":"Preference","screenshot_filename":"Preference.png","report_screenshot":"Preference.png","resolved_screenshot":"screenshots/Preference.png","action_id_screenshot":"screenshots/Preference.png"},{"name":"Tap on Text: \"Wi-Fi\"","status":"unknown","duration":"3310ms","action_id":"placeholder_report","screenshot_filename":"placeholder_report.png","report_screenshot":"placeholder_report.png","resolved_screenshot":"screenshots/placeholder_report.png","clean_action_id":"placeholder_report","prefixed_action_id":"al_placeholder_report","action_id_screenshot":"screenshots/placeholder_report.png"},{"name":"Tap on element with xpath: //XCUIElementTypeSwitch[@name=\"Wi‑Fi\"]","status":"unknown","duration":"1034ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Restart app: env[appid]","status":"unknown","duration":"3220ms","action_id":"placeholder_report","screenshot_filename":"placeholder_report.png","report_screenshot":"placeholder_report.png","resolved_screenshot":"screenshots/placeholder_report.png","clean_action_id":"placeholder_report","prefixed_action_id":"al_placeholder_report","action_id_screenshot":"screenshots/placeholder_report.png"},{"name":"Check if element with xpath=\"//XCUIElementTypeStaticText[@name=\"txtNo internet connection\"]\" exists","status":"unknown","duration":"235ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"2 of 5\")]","status":"unknown","duration":"724ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Check if element with xpath=\"//XCUIElementTypeStaticText[@name=\"txtNo internet connection\"]\" exists","status":"unknown","duration":"207ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"3 of 5\")]","status":"unknown","duration":"652ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Check if element with xpath=\"//XCUIElementTypeStaticText[@name=\"txtNo internet connection\"]\" exists","status":"unknown","duration":"214ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"4 of 5\")]","status":"unknown","duration":"681ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Check if element with xpath=\"//XCUIElementTypeStaticText[@name=\"txtNo internet connection\"]\" exists","status":"unknown","duration":"176ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Launch app: com.apple.Preferences","status":"unknown","duration":"125ms","action_id":"Preference","screenshot_filename":"Preference.png","report_screenshot":"Preference.png","resolved_screenshot":"screenshots/Preference.png","action_id_screenshot":"screenshots/Preference.png"},{"name":"Tap on element with xpath: //XCUIElementTypeSwitch[@name=\"Wi‑Fi\"]","status":"unknown","duration":"762ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Wait for 5 ms","status":"unknown","duration":"5016ms","action_id":"placeholder_report","screenshot_filename":"placeholder_report.png","report_screenshot":"placeholder_report.png","resolved_screenshot":"screenshots/placeholder_report.png","clean_action_id":"placeholder_report","prefixed_action_id":"al_placeholder_report","action_id_screenshot":"screenshots/placeholder_report.png"},{"name":"Restart app: env[appid]","status":"unknown","duration":"3213ms","action_id":"placeholder_report","screenshot_filename":"placeholder_report.png","report_screenshot":"placeholder_report.png","resolved_screenshot":"screenshots/placeholder_report.png","clean_action_id":"placeholder_report","prefixed_action_id":"al_placeholder_report","action_id_screenshot":"screenshots/placeholder_report.png"},{"name":"Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"5 of 5\")]","status":"unknown","duration":"681ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Swipe from (50%, 70%) to (50%, 30%)","status":"unknown","duration":"0ms","action_id":"placeholder_report","screenshot_filename":"placeholder_report.png","report_screenshot":"placeholder_report.png","resolved_screenshot":"screenshots/placeholder_report.png","clean_action_id":"placeholder_report","prefixed_action_id":"al_placeholder_report","action_id_screenshot":"screenshots/placeholder_report.png"},{"name":"Tap on Text: \"out\"","status":"unknown","duration":"0ms","action_id":"placeholder_report","screenshot_filename":"placeholder_report.png","report_screenshot":"placeholder_report.png","resolved_screenshot":"screenshots/placeholder_report.png","clean_action_id":"placeholder_report","prefixed_action_id":"al_placeholder_report","action_id_screenshot":"screenshots/placeholder_report.png"},{"name":"Restart app: com.apple.mobilesafari","status":"unknown","duration":"0ms","action_id":"mobilesafa","screenshot_filename":"mobilesafa.png","report_screenshot":"mobilesafa.png","resolved_screenshot":"screenshots/mobilesafa.png","action_id_screenshot":"screenshots/mobilesafa.png"},{"name":"Tap on element with xpath: //XCUIElementTypeTextField[@name=\"TabBarItemTitle\"]","status":"unknown","duration":"0ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"iOS Function: text","status":"unknown","duration":"0ms","action_id":"placeholder_report","screenshot_filename":"placeholder_report.png","report_screenshot":"placeholder_report.png","resolved_screenshot":"screenshots/placeholder_report.png","clean_action_id":"placeholder_report","prefixed_action_id":"al_placeholder_report","action_id_screenshot":"screenshots/placeholder_report.png"},{"name":"Swipe from (50%, 70%) to (50%, 30%)","status":"unknown","duration":"0ms","action_id":"placeholder_report","screenshot_filename":"placeholder_report.png","report_screenshot":"placeholder_report.png","resolved_screenshot":"screenshots/placeholder_report.png","clean_action_id":"placeholder_report","prefixed_action_id":"al_placeholder_report","action_id_screenshot":"screenshots/placeholder_report.png"},{"name":"Tap on element with xpath: //XCUIElementTypeStaticText[@name=\"https://www.kmart.com.au\"]","status":"unknown","duration":"0ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Check if element with xpath=\"//XCUIElementTypeButton[@name=\"txtHomeAccountCtaSignIn\"]\" exists","status":"unknown","duration":"0ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"2 of 5\")]","status":"unknown","duration":"681ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Tap on Text: \"Catalogue\"","status":"unknown","duration":"0ms","action_id":"placeholder_report","screenshot_filename":"placeholder_report.png","report_screenshot":"placeholder_report.png","resolved_screenshot":"screenshots/placeholder_report.png","clean_action_id":"placeholder_report","prefixed_action_id":"al_placeholder_report","action_id_screenshot":"screenshots/placeholder_report.png"},{"name":"If exists: xpath=\"//XCUIElementTypeOther[@name=\"Kmart Catalogue\"]/XCUIElementTypeImage[1]\" (timeout: 20s) → Then click element: xpath=\"//XCUIElementTypeOther[@name=\"Kmart Catalogue\"]/XCUIElementTypeImage[1]\"","status":"unknown","duration":"7212ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Tap on image: env[catalogue-menu-img]","status":"unknown","duration":"0ms","action_id":"placeholder_report","screenshot_filename":"placeholder_report.png","report_screenshot":"placeholder_report.png","resolved_screenshot":"screenshots/placeholder_report.png","clean_action_id":"placeholder_report","prefixed_action_id":"al_placeholder_report","action_id_screenshot":"screenshots/placeholder_report.png"},{"name":"Tap on Text: \"List\"","status":"unknown","duration":"0ms","action_id":"placeholder_report","screenshot_filename":"placeholder_report.png","report_screenshot":"placeholder_report.png","resolved_screenshot":"screenshots/placeholder_report.png","clean_action_id":"placeholder_report","prefixed_action_id":"al_placeholder_report","action_id_screenshot":"screenshots/placeholder_report.png"},{"name":"Tap on element with xpath: (//XCUIElementTypeOther[@name=\"Kmart Catalogue\"]//XCUIElementTypeStaticText[contains(@name,\"$\")])[1]","status":"unknown","duration":"0ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Tap on element with accessibility_id: Add to bag","status":"unknown","duration":"0ms","action_id":"accessibil","screenshot_filename":"accessibil.png","report_screenshot":"accessibil.png","resolved_screenshot":"screenshots/accessibil.png","action_id_screenshot":"screenshots/accessibil.png"},{"name":"Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"5 of 5\")]","status":"unknown","duration":"681ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Tap on Text: \"Catalogue\"","status":"unknown","duration":"0ms","action_id":"placeholder_report","screenshot_filename":"placeholder_report.png","report_screenshot":"placeholder_report.png","resolved_screenshot":"screenshots/placeholder_report.png","clean_action_id":"placeholder_report","prefixed_action_id":"al_placeholder_report","action_id_screenshot":"screenshots/placeholder_report.png"},{"name":"If exists: xpath=\"//XCUIElementTypeOther[@name=\"Kmart Catalogue\"]/XCUIElementTypeImage[1]\" (timeout: 20s) → Then click element: xpath=\"//XCUIElementTypeOther[@name=\"Kmart Catalogue\"]/XCUIElementTypeImage[1]\"","status":"unknown","duration":"7212ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Tap on image: env[catalogue-menu-img]","status":"unknown","duration":"0ms","action_id":"placeholder_report","screenshot_filename":"placeholder_report.png","report_screenshot":"placeholder_report.png","resolved_screenshot":"screenshots/placeholder_report.png","clean_action_id":"placeholder_report","prefixed_action_id":"al_placeholder_report","action_id_screenshot":"screenshots/placeholder_report.png"},{"name":"Tap on Text: \"List\"","status":"unknown","duration":"0ms","action_id":"placeholder_report","screenshot_filename":"placeholder_report.png","report_screenshot":"placeholder_report.png","resolved_screenshot":"screenshots/placeholder_report.png","clean_action_id":"placeholder_report","prefixed_action_id":"al_placeholder_report","action_id_screenshot":"screenshots/placeholder_report.png"},{"name":"Tap on element with xpath: (//XCUIElementTypeOther[@name=\"Kmart Catalogue\"]//XCUIElementTypeStaticText[contains(@name,\"$\")])[1]","status":"unknown","duration":"0ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Tap on element with accessibility_id: Add to bag","status":"unknown","duration":"0ms","action_id":"accessibil","screenshot_filename":"accessibil.png","report_screenshot":"accessibil.png","resolved_screenshot":"screenshots/accessibil.png","action_id_screenshot":"screenshots/accessibil.png"},{"name":"Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"4 of 5\")]","status":"unknown","duration":"681ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Tap on element with xpath: //XCUIElementTypeButton[@name=\"Increase quantity\"]","status":"unknown","duration":"0ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Tap on element with xpath: //XCUIElementTypeButton[@name=\"Decrease quantity\"]","status":"unknown","duration":"0ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Remove\")]","status":"unknown","duration":"0ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Tap on image: banner-close-updated.png","status":"unknown","duration":"0ms","action_id":"placeholder_report","screenshot_filename":"placeholder_report.png","report_screenshot":"placeholder_report.png","resolved_screenshot":"screenshots/placeholder_report.png","clean_action_id":"placeholder_report","prefixed_action_id":"al_placeholder_report","action_id_screenshot":"screenshots/placeholder_report.png"},{"name":"Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"1 of 5\")]","status":"unknown","duration":"724ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"swipeTillVisible action","status":"unknown","duration":"0ms","action_id":"swipeTillV","screenshot_filename":"swipeTillV.png","report_screenshot":"swipeTillV.png","resolved_screenshot":"screenshots/swipeTillV.png","action_id_screenshot":"screenshots/swipeTillV.png"},{"name":"Tap on element with xpath: //XCUIElementTypeButton[@name=\"Home & Living\"]","status":"unknown","duration":"0ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Tap on element with xpath: (//XCUIElementTypeScrollView)[4]/following-sibling::XCUIElementTypeOther[1]","status":"unknown","duration":"0ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Tap on element with xpath: (//XCUIElementTypeScrollView/following-sibling::XCUIElementTypeImage[contains(@name,\"$\")])[1]","status":"unknown","duration":"0ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"swipeTillVisible action","status":"unknown","duration":"0ms","action_id":"swipeTillV","screenshot_filename":"swipeTillV.png","report_screenshot":"swipeTillV.png","resolved_screenshot":"screenshots/swipeTillV.png","action_id_screenshot":"screenshots/swipeTillV.png"},{"name":"Tap on element with xpath: (//XCUIElementTypeOther[@name=\"You may also like\"]/following-sibling::*[1]//XCUIElementTypeLink)[1]","status":"unknown","duration":"0ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Tap on element with accessibility_id: Add to bag","status":"unknown","duration":"0ms","action_id":"accessibil","screenshot_filename":"accessibil.png","report_screenshot":"accessibil.png","resolved_screenshot":"screenshots/accessibil.png","action_id_screenshot":"screenshots/accessibil.png"},{"name":"Restart app: env[appid]","status":"unknown","duration":"0ms","action_id":"placeholder_report","screenshot_filename":"placeholder_report.png","report_screenshot":"placeholder_report.png","resolved_screenshot":"screenshots/placeholder_report.png","clean_action_id":"placeholder_report","prefixed_action_id":"al_placeholder_report","action_id_screenshot":"screenshots/placeholder_report.png"},{"name":"Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"4 of 5\")]","status":"unknown","duration":"681ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Remove\")]","status":"unknown","duration":"0ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"}]}],"passed":1,"failed":0,"skipped":0,"status":"passed","availableScreenshots":["XEbZHdi0GT.png","rJ86z4njuR.png","veukWo4573.png"],"screenshots_map":{}};
    </script>

    <script src="./assets/report.js"></script>
</body>
</html>