Action Log - 2025-06-15 12:51:38
================================================================================

[[12:51:38]] [INFO] Generating execution report...
[[12:51:38]] [SUCCESS] All tests passed successfully!
[[12:51:37]] [SUCCESS] Screenshot refreshed
[[12:51:37]] [INFO] Refreshing screenshot...
[[12:51:36]] [SUCCESS] Screenshot refreshed successfully
[[12:51:36]] [SUCCESS] Screenshot refreshed successfully
[[12:51:35]] [INFO] Executing action 25/25: Add Log: App is closed (with screenshot)
[[12:51:35]] [SUCCESS] Screenshot refreshed
[[12:51:35]] [INFO] Refreshing screenshot...
[[12:51:34]] [SUCCESS] Screenshot refreshed
[[12:51:34]] [INFO] Refreshing screenshot...
[[12:51:33]] [SUCCESS] Screenshot refreshed successfully
[[12:51:33]] [SUCCESS] Screenshot refreshed successfully
[[12:51:32]] [INFO] Executing Multi Step action step 9/9: Add Log: App is closed (with screenshot)
[[12:51:32]] [SUCCESS] Screenshot refreshed
[[12:51:32]] [INFO] Refreshing screenshot...
[[12:51:19]] [SUCCESS] Screenshot refreshed successfully
[[12:51:19]] [SUCCESS] Screenshot refreshed successfully
[[12:51:18]] [INFO] Executing Multi Step action step 8/9: Execute Test Case: apple health (8 steps)
[[12:51:17]] [SUCCESS] Screenshot refreshed
[[12:51:17]] [INFO] Refreshing screenshot...
[[12:51:14]] [SUCCESS] Screenshot refreshed successfully
[[12:51:14]] [SUCCESS] Screenshot refreshed successfully
[[12:51:14]] [INFO] Executing Multi Step action step 7/9: Terminate app: com.apple.Health
[[12:51:14]] [SUCCESS] Screenshot refreshed
[[12:51:14]] [INFO] Refreshing screenshot...
[[12:51:10]] [INFO] Executing Multi Step action step 6/9: Click element: xpath= //XCUIElementTypeButton[@name="Done"]
[[12:51:10]] [SUCCESS] Screenshot refreshed successfully
[[12:51:10]] [SUCCESS] Screenshot refreshed successfully
[[12:51:10]] [SUCCESS] Screenshot refreshed
[[12:51:10]] [INFO] Refreshing screenshot...
[[12:51:08]] [INFO] Executing Multi Step action step 5/9: Add Log: Clicked on Edit link successfully (with screenshot)
[[12:51:08]] [SUCCESS] Screenshot refreshed successfully
[[12:51:08]] [SUCCESS] Screenshot refreshed successfully
[[12:51:08]] [SUCCESS] Screenshot refreshed
[[12:51:08]] [INFO] Refreshing screenshot...
[[12:51:05]] [SUCCESS] Screenshot refreshed successfully
[[12:51:05]] [SUCCESS] Screenshot refreshed successfully
[[12:51:05]] [INFO] Executing Multi Step action step 4/9: Click element: xpath=//XCUIElementTypeStaticText[@name="Edit"]
[[12:51:05]] [SUCCESS] Screenshot refreshed
[[12:51:05]] [INFO] Refreshing screenshot...
[[12:51:02]] [INFO] Executing Multi Step action step 3/9: Click element: xpath= //XCUIElementTypeButton[@name="Done"]
[[12:51:02]] [SUCCESS] Screenshot refreshed successfully
[[12:51:02]] [SUCCESS] Screenshot refreshed successfully
[[12:51:01]] [SUCCESS] Screenshot refreshed
[[12:51:01]] [INFO] Refreshing screenshot...
[[12:50:59]] [SUCCESS] Screenshot refreshed successfully
[[12:50:59]] [SUCCESS] Screenshot refreshed successfully
[[12:50:59]] [INFO] Executing Multi Step action step 2/9: Click element: xpath=//XCUIElementTypeStaticText[@name="Edit"]
[[12:50:58]] [SUCCESS] Screenshot refreshed
[[12:50:58]] [INFO] Refreshing screenshot...
[[12:50:54]] [SUCCESS] Screenshot refreshed successfully
[[12:50:54]] [SUCCESS] Screenshot refreshed successfully
[[12:50:53]] [INFO] Executing Multi Step action step 1/9: Launch app: com.apple.Health
[[12:50:53]] [INFO] Loaded 9 steps from test case: apple health
[[12:50:53]] [INFO] Loading steps for Multi Step action: apple health
[[12:50:53]] [INFO] Executing action 24/25: Execute Test Case: apple health (8 steps)
[[12:50:53]] [SUCCESS] Screenshot refreshed
[[12:50:53]] [INFO] Refreshing screenshot...
[[12:50:50]] [SUCCESS] Screenshot refreshed successfully
[[12:50:50]] [SUCCESS] Screenshot refreshed successfully
[[12:50:50]] [INFO] Executing action 23/25: Terminate app: com.apple.Health
[[12:50:49]] [SUCCESS] Screenshot refreshed
[[12:50:49]] [INFO] Refreshing screenshot...
[[12:50:46]] [INFO] Executing action 22/25: Click element: xpath= //XCUIElementTypeButton[@name="Done"]
[[12:50:46]] [SUCCESS] Screenshot refreshed successfully
[[12:50:46]] [SUCCESS] Screenshot refreshed successfully
[[12:50:46]] [SUCCESS] Screenshot refreshed
[[12:50:46]] [INFO] Refreshing screenshot...
[[12:50:44]] [INFO] Executing action 21/25: Add Log: Clicked on Edit link successfully (with screenshot)
[[12:50:44]] [SUCCESS] Screenshot refreshed successfully
[[12:50:44]] [SUCCESS] Screenshot refreshed successfully
[[12:50:44]] [SUCCESS] Screenshot refreshed
[[12:50:44]] [INFO] Refreshing screenshot...
[[12:50:41]] [SUCCESS] Screenshot refreshed successfully
[[12:50:41]] [SUCCESS] Screenshot refreshed successfully
[[12:50:41]] [INFO] Executing action 20/25: Click element: xpath=//XCUIElementTypeStaticText[@name="Edit"]
[[12:50:40]] [SUCCESS] Screenshot refreshed
[[12:50:40]] [INFO] Refreshing screenshot...
[[12:50:37]] [INFO] Executing action 19/25: Click element: xpath= //XCUIElementTypeButton[@name="Done"]
[[12:50:37]] [SUCCESS] Screenshot refreshed successfully
[[12:50:37]] [SUCCESS] Screenshot refreshed successfully
[[12:50:36]] [SUCCESS] Screenshot refreshed
[[12:50:36]] [INFO] Refreshing screenshot...
[[12:50:34]] [SUCCESS] Screenshot refreshed successfully
[[12:50:34]] [SUCCESS] Screenshot refreshed successfully
[[12:50:34]] [INFO] Executing action 18/25: Click element: xpath=//XCUIElementTypeStaticText[@name="Edit"]
[[12:50:33]] [SUCCESS] Screenshot refreshed
[[12:50:33]] [INFO] Refreshing screenshot...
[[12:50:22]] [SUCCESS] Screenshot refreshed successfully
[[12:50:22]] [SUCCESS] Screenshot refreshed successfully
[[12:50:21]] [INFO] Executing action 17/25: Launch app: com.apple.Health
[[12:50:20]] [SUCCESS] Screenshot refreshed
[[12:50:20]] [INFO] Refreshing screenshot...
[[12:50:20]] [INFO] Skipping Hook Action during normal execution. This action will only be executed when a step fails.
[[12:50:20]] [INFO] Executing action 16/25: Hook Action: tap on element with xpath: //XCUIElementTypeStaticText[@name="Edit"] (Recovery)
[[12:50:20]] [SUCCESS] Screenshot refreshed
[[12:50:20]] [INFO] Refreshing screenshot...
[[12:50:20]] [SUCCESS] Screenshot refreshed
[[12:50:20]] [INFO] Refreshing screenshot...
[[12:50:18]] [SUCCESS] Screenshot refreshed successfully
[[12:50:18]] [SUCCESS] Screenshot refreshed successfully
[[12:50:17]] [INFO] Executing Multi Step action step 9/9: Add Log: App is closed (with screenshot)
[[12:50:17]] [SUCCESS] Screenshot refreshed
[[12:50:17]] [INFO] Refreshing screenshot...
[[12:50:14]] [SUCCESS] Screenshot refreshed successfully
[[12:50:14]] [SUCCESS] Screenshot refreshed successfully
[[12:50:14]] [INFO] Executing Multi Step action step 8/9: Terminate app: com.apple.Health
[[12:50:13]] [SUCCESS] Screenshot refreshed
[[12:50:13]] [INFO] Refreshing screenshot...
[[12:50:11]] [SUCCESS] Screenshot refreshed successfully
[[12:50:11]] [SUCCESS] Screenshot refreshed successfully
[[12:50:10]] [INFO] Executing Multi Step action step 7/9: Wait for 1 ms
[[12:50:10]] [SUCCESS] Screenshot refreshed
[[12:50:10]] [INFO] Refreshing screenshot...
[[12:50:08]] [SUCCESS] Screenshot refreshed successfully
[[12:50:08]] [SUCCESS] Screenshot refreshed successfully
[[12:50:08]] [INFO] Executing Multi Step action step 6/9: Add Log: Done link is clicked (with screenshot)
[[12:50:08]] [SUCCESS] Screenshot refreshed
[[12:50:08]] [INFO] Refreshing screenshot...
[[12:50:05]] [INFO] Executing Multi Step action step 5/9: Click element: xpath= //XCUIElementTypeButton[@name="Done"]
[[12:50:05]] [SUCCESS] Screenshot refreshed successfully
[[12:50:05]] [SUCCESS] Screenshot refreshed successfully
[[12:50:04]] [SUCCESS] Screenshot refreshed
[[12:50:04]] [INFO] Refreshing screenshot...
[[12:50:03]] [INFO] Executing Multi Step action step 4/9: Add Log: Edit link is clicked (with screenshot)
[[12:50:03]] [SUCCESS] Screenshot refreshed successfully
[[12:50:03]] [SUCCESS] Screenshot refreshed successfully
[[12:50:02]] [SUCCESS] Screenshot refreshed
[[12:50:02]] [INFO] Refreshing screenshot...
[[12:50:00]] [SUCCESS] Screenshot refreshed successfully
[[12:50:00]] [SUCCESS] Screenshot refreshed successfully
[[12:50:00]] [INFO] Executing Multi Step action step 3/9: Click element: xpath=//XCUIElementTypeStaticText[@name="Edit"]
[[12:49:59]] [SUCCESS] Screenshot refreshed
[[12:49:59]] [INFO] Refreshing screenshot...
[[12:49:57]] [SUCCESS] Screenshot refreshed successfully
[[12:49:57]] [SUCCESS] Screenshot refreshed successfully
[[12:49:57]] [INFO] Executing Multi Step action step 2/9: Add Log: Launched App Successfully (with screenshot)
[[12:49:57]] [SUCCESS] Screenshot refreshed
[[12:49:57]] [INFO] Refreshing screenshot...
[[12:49:53]] [SUCCESS] Screenshot refreshed successfully
[[12:49:53]] [SUCCESS] Screenshot refreshed successfully
[[12:49:52]] [INFO] Executing Multi Step action step 1/9: Launch app: com.apple.Health
[[12:49:52]] [INFO] Loaded 9 steps from test case: health2
[[12:49:52]] [INFO] Loading steps for Multi Step action: health2
[[12:49:52]] [INFO] Executing action 15/25: Execute Test Case: health2 (9 steps)
[[12:49:51]] [SUCCESS] Screenshot refreshed
[[12:49:51]] [INFO] Refreshing screenshot...
[[12:49:50]] [SUCCESS] Screenshot refreshed successfully
[[12:49:50]] [SUCCESS] Screenshot refreshed successfully
[[12:49:49]] [INFO] Executing action 14/25: Add Log: Closed App Successfully (with screenshot)
[[12:49:49]] [SUCCESS] Screenshot refreshed
[[12:49:49]] [INFO] Refreshing screenshot...
[[12:49:46]] [SUCCESS] Screenshot refreshed successfully
[[12:49:46]] [SUCCESS] Screenshot refreshed successfully
[[12:49:46]] [INFO] Executing action 13/25: Terminate app: com.apple.Health
[[12:49:45]] [SUCCESS] Screenshot refreshed
[[12:49:45]] [INFO] Refreshing screenshot...
[[12:49:42]] [INFO] Executing action 12/25: Click element: xpath= //XCUIElementTypeButton[@name="Done"]
[[12:49:42]] [SUCCESS] Screenshot refreshed successfully
[[12:49:42]] [SUCCESS] Screenshot refreshed successfully
[[12:49:42]] [SUCCESS] Screenshot refreshed
[[12:49:42]] [INFO] Refreshing screenshot...
[[12:49:39]] [SUCCESS] Screenshot refreshed successfully
[[12:49:39]] [SUCCESS] Screenshot refreshed successfully
[[12:49:39]] [INFO] Executing action 11/25: Tap on element with xpath: //XCUIElementTypeStaticText[@name="Edit"]
[[12:49:39]] [SUCCESS] Screenshot refreshed
[[12:49:39]] [INFO] Refreshing screenshot...
[[12:49:27]] [SUCCESS] Screenshot refreshed successfully
[[12:49:27]] [SUCCESS] Screenshot refreshed successfully
[[12:49:26]] [INFO] Executing action 10/25: Launch app: com.apple.Health
[[12:49:26]] [SUCCESS] Screenshot refreshed
[[12:49:26]] [INFO] Refreshing screenshot...
[[12:49:24]] [SUCCESS] Screenshot refreshed successfully
[[12:49:24]] [SUCCESS] Screenshot refreshed successfully
[[12:49:23]] [INFO] Executing action 9/25: Add Log: App is closed (with screenshot)
[[12:49:23]] [SUCCESS] Screenshot refreshed
[[12:49:23]] [INFO] Refreshing screenshot...
[[12:49:20]] [SUCCESS] Screenshot refreshed successfully
[[12:49:20]] [SUCCESS] Screenshot refreshed successfully
[[12:49:20]] [INFO] Executing action 8/25: Terminate app: com.apple.Health
[[12:49:19]] [SUCCESS] Screenshot refreshed
[[12:49:19]] [INFO] Refreshing screenshot...
[[12:49:17]] [SUCCESS] Screenshot refreshed successfully
[[12:49:17]] [SUCCESS] Screenshot refreshed successfully
[[12:49:17]] [INFO] Executing action 7/25: Wait for 1 ms
[[12:49:16]] [SUCCESS] Screenshot refreshed
[[12:49:16]] [INFO] Refreshing screenshot...
[[12:49:15]] [SUCCESS] Screenshot refreshed successfully
[[12:49:15]] [SUCCESS] Screenshot refreshed successfully
[[12:49:15]] [INFO] Executing action 6/25: Add Log: Done link is clicked (with screenshot)
[[12:49:14]] [SUCCESS] Screenshot refreshed
[[12:49:14]] [INFO] Refreshing screenshot...
[[12:49:11]] [INFO] Executing action 5/25: Click element: xpath= //XCUIElementTypeButton[@name="Done"]
[[12:49:11]] [SUCCESS] Screenshot refreshed successfully
[[12:49:11]] [SUCCESS] Screenshot refreshed successfully
[[12:49:10]] [SUCCESS] Screenshot refreshed
[[12:49:10]] [INFO] Refreshing screenshot...
[[12:49:09]] [INFO] Executing action 4/25: Add Log: Edit link is clicked (with screenshot)
[[12:49:09]] [SUCCESS] Screenshot refreshed successfully
[[12:49:09]] [SUCCESS] Screenshot refreshed successfully
[[12:49:08]] [SUCCESS] Screenshot refreshed
[[12:49:08]] [INFO] Refreshing screenshot...
[[12:49:06]] [SUCCESS] Screenshot refreshed successfully
[[12:49:06]] [SUCCESS] Screenshot refreshed successfully
[[12:49:06]] [INFO] Executing action 3/25: Click element: xpath=//XCUIElementTypeStaticText[@name="Edit"]
[[12:49:05]] [SUCCESS] Screenshot refreshed
[[12:49:05]] [INFO] Refreshing screenshot...
[[12:49:04]] [SUCCESS] Screenshot refreshed successfully
[[12:49:04]] [SUCCESS] Screenshot refreshed successfully
[[12:49:04]] [INFO] Executing action 2/25: Add Log: Launched App Successfully (with screenshot)
[[12:49:03]] [SUCCESS] Screenshot refreshed
[[12:49:03]] [INFO] Refreshing screenshot...
[[12:49:00]] [INFO] Executing action 1/25: Launch app: com.apple.Health
[[12:49:00]] [INFO] ExecutionManager: Starting execution of 25 actions...
[[12:49:00]] [WARNING] Failed to clear screenshots: Error clearing screenshots: No module named 'app.utils'; 'app' is not a package
[[12:49:00]] [INFO] Clearing screenshots from database before execution...
[[12:49:00]] [SUCCESS] All screenshots deleted successfully
[[12:49:00]] [INFO] Deleting all screenshots in app/static/screenshots folder...
[[12:49:00]] [INFO] Screenshots directory: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/reports/testsuite_execution_20250615_124900/screenshots
[[12:49:00]] [INFO] Report directory: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/reports/testsuite_execution_20250615_124900
[[12:49:00]] [SUCCESS] Report directory initialized successfully
[[12:49:00]] [INFO] Initializing report directory and screenshots folder...
[[12:48:45]] [SUCCESS] All screenshots deleted successfully
[[12:48:45]] [INFO] All actions cleared
[[12:48:45]] [INFO] Cleaning up screenshots...
[[12:48:42]] [SUCCESS] Screenshot refreshed successfully
[[12:48:41]] [SUCCESS] Screenshot refreshed
[[12:48:41]] [INFO] Refreshing screenshot...
[[12:48:40]] [SUCCESS] Connected to device: 00008120-00186C801E13C01E with AirTest support
[[12:48:40]] [INFO] Device info updated: 00008120-00186C801E13C01E
[[12:48:37]] [INFO] Connecting to device: 00008120-00186C801E13C01E (Platform: iOS)...
[[12:48:34]] [SUCCESS] Found 1 device(s)
[[12:48:33]] [INFO] Refreshing device list...
