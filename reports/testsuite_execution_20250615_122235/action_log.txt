Action Log - 2025-06-15 12:27:03
================================================================================

[[12:27:03]] [INFO] Generating execution report...
[[12:27:03]] [SUCCESS] All tests passed successfully!
[[12:27:03]] [INFO] Moving to the next test case after failure (server will handle retry)
[[12:27:03]] [ERROR] Error executing Multi Step action step 1: Failed to fetch
[[12:25:21]] [SUCCESS] Screenshot refreshed successfully
[[12:25:21]] [SUCCESS] Screenshot refreshed successfully
[[12:25:20]] [INFO] Executing Multi Step action step 1/10: Swipe from (50%, 80%) to (50%, 10%)
[[12:25:20]] [INFO] Loaded 10 steps from test case: Click_Paginations
[[12:25:20]] [INFO] Loading steps for Multi Step action: Click_Paginations
[[12:25:20]] [INFO] Executing action 21/21: Execute Test Case: Click_Paginations (10 steps)
[[12:25:20]] [SUCCESS] Screenshot refreshed
[[12:25:20]] [INFO] Refreshing screenshot...
[[12:25:16]] [SUCCESS] Screenshot refreshed successfully
[[12:25:16]] [SUCCESS] Screenshot refreshed successfully
[[12:25:15]] [INFO] Executing action 20/21: iOS Function: text
[[12:25:15]] [SUCCESS] Screenshot refreshed
[[12:25:15]] [INFO] Refreshing screenshot...
[[12:25:10]] [SUCCESS] Screenshot refreshed successfully
[[12:25:10]] [SUCCESS] Screenshot refreshed successfully
[[12:25:09]] [INFO] Executing action 19/21: Tap on Text: "Find"
[[12:25:09]] [SUCCESS] Screenshot refreshed
[[12:25:09]] [INFO] Refreshing screenshot...
[[12:25:05]] [SUCCESS] Screenshot refreshed successfully
[[12:25:05]] [SUCCESS] Screenshot refreshed successfully
[[12:25:05]] [INFO] Executing action 18/21: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 1 of 5")]
[[12:25:04]] [SUCCESS] Screenshot refreshed
[[12:25:04]] [INFO] Refreshing screenshot...
[[12:25:03]] [SUCCESS] Screenshot refreshed successfully
[[12:25:03]] [SUCCESS] Screenshot refreshed successfully
[[12:25:03]] [INFO] Executing action 17/21: Launch app: env[appid]
[[12:25:02]] [SUCCESS] Screenshot refreshed
[[12:25:02]] [INFO] Refreshing screenshot...
[[12:24:58]] [SUCCESS] Screenshot refreshed successfully
[[12:24:58]] [SUCCESS] Screenshot refreshed successfully
[[12:24:58]] [INFO] Executing action 16/21: Tap on Text: "+61"
[[12:24:57]] [SUCCESS] Screenshot refreshed
[[12:24:57]] [INFO] Refreshing screenshot...
[[12:24:53]] [SUCCESS] Screenshot refreshed successfully
[[12:24:53]] [SUCCESS] Screenshot refreshed successfully
[[12:24:53]] [INFO] Executing action 15/21: Tap on Text: "1800"
[[12:24:52]] [SUCCESS] Screenshot refreshed
[[12:24:52]] [INFO] Refreshing screenshot...
[[12:24:48]] [SUCCESS] Screenshot refreshed successfully
[[12:24:48]] [SUCCESS] Screenshot refreshed successfully
[[12:24:48]] [INFO] Executing action 14/21: Tap on Text: "click"
[[12:24:47]] [SUCCESS] Screenshot refreshed
[[12:24:47]] [INFO] Refreshing screenshot...
[[12:24:29]] [SUCCESS] Screenshot refreshed successfully
[[12:24:29]] [SUCCESS] Screenshot refreshed successfully
[[12:24:29]] [INFO] Executing action 13/21: Swipe from (50%, 70%) to (50%, 30%)
[[12:24:28]] [SUCCESS] Screenshot refreshed
[[12:24:28]] [INFO] Refreshing screenshot...
[[12:24:24]] [SUCCESS] Screenshot refreshed successfully
[[12:24:24]] [SUCCESS] Screenshot refreshed successfully
[[12:24:24]] [INFO] Executing action 12/21: Tap on Text: "FAQ"
[[12:24:23]] [SUCCESS] Screenshot refreshed
[[12:24:23]] [INFO] Refreshing screenshot...
[[12:24:19]] [SUCCESS] Screenshot refreshed successfully
[[12:24:19]] [SUCCESS] Screenshot refreshed successfully
[[12:24:19]] [INFO] Executing action 11/21: Tap on Text: "Help"
[[12:24:19]] [SUCCESS] Screenshot refreshed
[[12:24:19]] [INFO] Refreshing screenshot...
[[12:24:16]] [SUCCESS] Screenshot refreshed successfully
[[12:24:16]] [SUCCESS] Screenshot refreshed successfully
[[12:24:15]] [INFO] Executing action 10/21: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[12:24:15]] [SUCCESS] Screenshot refreshed
[[12:24:15]] [INFO] Refreshing screenshot...
[[12:24:10]] [SUCCESS] Screenshot refreshed successfully
[[12:24:10]] [SUCCESS] Screenshot refreshed successfully
[[12:24:09]] [INFO] Executing action 9/21: Restart app: env[appid]
[[12:24:09]] [SUCCESS] Screenshot refreshed
[[12:24:09]] [INFO] Refreshing screenshot...
[[12:24:03]] [SUCCESS] Screenshot refreshed successfully
[[12:24:03]] [SUCCESS] Screenshot refreshed successfully
[[12:24:03]] [INFO] Executing action 8/21: Tap on Text: "Done"
[[12:24:02]] [SUCCESS] Screenshot refreshed
[[12:24:02]] [INFO] Refreshing screenshot...
[[12:23:59]] [SUCCESS] Screenshot refreshed successfully
[[12:23:59]] [SUCCESS] Screenshot refreshed successfully
[[12:23:58]] [INFO] Executing action 7/21: Tap on element with xpath: //XCUIElementTypeStaticText[@name="Instruction Manual"]
[[12:23:58]] [SUCCESS] Screenshot refreshed
[[12:23:58]] [INFO] Refreshing screenshot...
[[12:23:28]] [SUCCESS] Screenshot refreshed successfully
[[12:23:28]] [SUCCESS] Screenshot refreshed successfully
[[12:23:28]] [INFO] Executing action 6/21: Swipe from (50%, 80%) to (50%, 10%)
[[12:23:27]] [SUCCESS] Screenshot refreshed
[[12:23:27]] [INFO] Refreshing screenshot...
[[12:22:57]] [SUCCESS] Screenshot refreshed successfully
[[12:22:57]] [SUCCESS] Screenshot refreshed successfully
[[12:22:57]] [INFO] Executing action 5/21: Swipe from (50%, 70%) to (50%, 10%)
[[12:22:56]] [SUCCESS] Screenshot refreshed
[[12:22:56]] [INFO] Refreshing screenshot...
[[12:22:53]] [SUCCESS] Screenshot refreshed successfully
[[12:22:53]] [SUCCESS] Screenshot refreshed successfully
[[12:22:53]] [INFO] Executing action 4/21: Tap on element with xpath: (//XCUIElementTypeOther[@name="Sort by: Relevance"]/following-sibling::XCUIElementTypeOther[1]//XCUIElementTypeLink)[1]
[[12:22:52]] [SUCCESS] Screenshot refreshed
[[12:22:52]] [INFO] Refreshing screenshot...
[[12:22:48]] [SUCCESS] Screenshot refreshed successfully
[[12:22:48]] [SUCCESS] Screenshot refreshed successfully
[[12:22:48]] [INFO] Executing action 3/21: iOS Function: text
[[12:22:47]] [SUCCESS] Screenshot refreshed
[[12:22:47]] [INFO] Refreshing screenshot...
[[12:22:41]] [SUCCESS] Screenshot refreshed successfully
[[12:22:41]] [SUCCESS] Screenshot refreshed successfully
[[12:22:41]] [INFO] Executing action 2/21: Tap on Text: "Find"
[[12:22:40]] [SUCCESS] Screenshot refreshed
[[12:22:40]] [INFO] Refreshing screenshot...
[[12:22:35]] [INFO] Executing action 1/21: Restart app: env[appid]
[[12:22:35]] [INFO] ExecutionManager: Starting execution of 21 actions...
[[12:22:35]] [WARNING] Failed to clear screenshots: Error clearing screenshots: No module named 'app.utils'; 'app' is not a package
[[12:22:35]] [INFO] Clearing screenshots from database before execution...
[[12:22:35]] [SUCCESS] All screenshots deleted successfully
[[12:22:35]] [INFO] Deleting all screenshots in app/static/screenshots folder...
[[12:22:35]] [INFO] Screenshots directory: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/reports/testsuite_execution_20250615_122235/screenshots
[[12:22:35]] [INFO] Report directory: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/reports/testsuite_execution_20250615_122235
[[12:22:35]] [SUCCESS] Report directory initialized successfully
[[12:22:35]] [INFO] Initializing report directory and screenshots folder...
[[12:22:32]] [SUCCESS] All screenshots deleted successfully
[[12:22:32]] [SUCCESS] Loaded test case "temp" with 21 actions
[[12:22:32]] [SUCCESS] Added action: multiStep
[[12:22:32]] [SUCCESS] Added action: iosFunctions
[[12:22:32]] [SUCCESS] Added action: tapOnText
[[12:22:32]] [SUCCESS] Added action: tap
[[12:22:32]] [SUCCESS] Added action: launchApp
[[12:22:32]] [SUCCESS] Added action: tapOnText
[[12:22:32]] [SUCCESS] Added action: tapOnText
[[12:22:32]] [SUCCESS] Added action: tapOnText
[[12:22:32]] [SUCCESS] Added action: swipe
[[12:22:32]] [SUCCESS] Added action: tapOnText
[[12:22:32]] [SUCCESS] Added action: tapOnText
[[12:22:32]] [SUCCESS] Added action: tap
[[12:22:32]] [SUCCESS] Added action: restartApp
[[12:22:32]] [SUCCESS] Added action: tapOnText
[[12:22:32]] [SUCCESS] Added action: tap
[[12:22:32]] [SUCCESS] Added action: swipe
[[12:22:32]] [SUCCESS] Added action: swipe
[[12:22:32]] [SUCCESS] Added action: tap
[[12:22:32]] [SUCCESS] Added action: iosFunctions
[[12:22:32]] [SUCCESS] Added action: tapOnText
[[12:22:32]] [SUCCESS] Added action: restartApp
[[12:22:32]] [INFO] All actions cleared
[[12:22:32]] [INFO] Cleaning up screenshots...
[[12:22:31]] [SUCCESS] Screenshot refreshed successfully
[[12:22:30]] [SUCCESS] Screenshot refreshed
[[12:22:30]] [INFO] Refreshing screenshot...
[[12:22:29]] [SUCCESS] Connected to device: 00008120-00186C801E13C01E with AirTest support
[[12:22:29]] [INFO] Device info updated: 00008120-00186C801E13C01E
[[12:22:26]] [INFO] Connecting to device: 00008120-00186C801E13C01E (Platform: iOS)...
[[12:22:25]] [SUCCESS] Found 1 device(s)
[[12:22:24]] [INFO] Refreshing device list...
