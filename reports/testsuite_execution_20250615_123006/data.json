{"name": "UI Execution 15/06/2025, 12:41:49", "testCases": [{"name": "Test Case", "status": "passed", "steps": [{"name": "Restart app: env[appid]", "status": "passed", "duration": "2388ms", "action_id": "9Jhn4eWZwR", "screenshot_filename": "9Jhn4eWZwR.png", "report_screenshot": "9Jhn4eWZwR.png", "resolved_screenshot": "screenshots/9Jhn4eWZwR.png", "clean_action_id": "9Jhn4eWZwR", "prefixed_action_id": "al_9Jhn4eWZwR", "action_id_screenshot": "screenshots/9Jhn4eWZwR.png"}, {"name": "Tap on Text: \"Find\"", "status": "passed", "duration": "4733ms", "action_id": "yNAxs8bgMy", "screenshot_filename": "yNAxs8bgMy.png", "report_screenshot": "yNAxs8bgMy.png", "resolved_screenshot": "screenshots/yNAxs8bgMy.png", "clean_action_id": "yNAxs8bgMy", "prefixed_action_id": "al_yNAxs8bgMy", "action_id_screenshot": "screenshots/yNAxs8bgMy.png"}, {"name": "iOS Function: text", "status": "passed", "duration": "2268ms", "action_id": "g17Boaefhg", "screenshot_filename": "g17Boaefhg.png", "report_screenshot": "g17Boaefhg.png", "resolved_screenshot": "screenshots/g17Boaefhg.png", "clean_action_id": "g17Boaefhg", "prefixed_action_id": "al_g17Boaefhg", "action_id_screenshot": "screenshots/g17Boaefhg.png"}, {"name": "Tap on element with xpath: (//XCUIElementTypeOther[@name=\"Sort by: Relevance\"]/following-sibling::XCUIElementTypeOther[1]//XCUIElementTypeLink)[1]", "status": "passed", "duration": "1973ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Swipe from (50%, 70%) to (50%, 10%)", "status": "passed", "duration": "14128ms", "action_id": "L8LEfGm9WC", "screenshot_filename": "L8LEfGm9WC.png", "report_screenshot": "L8LEfGm9WC.png", "resolved_screenshot": "screenshots/L8LEfGm9WC.png", "clean_action_id": "L8LEfGm9WC", "prefixed_action_id": "al_L8LEfGm9WC", "action_id_screenshot": "screenshots/L8LEfGm9WC.png"}, {"name": "Swipe from (50%, 80%) to (50%, 10%)", "status": "passed", "duration": "15777ms", "action_id": "88BYVcWtJZ", "screenshot_filename": "88BYVcWtJZ.png", "report_screenshot": "88BYVcWtJZ.png", "resolved_screenshot": "screenshots/88BYVcWtJZ.png", "clean_action_id": "88BYVcWtJZ", "prefixed_action_id": "al_88BYVcWtJZ", "action_id_screenshot": "screenshots/88BYVcWtJZ.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeStaticText[@name=\"Instruction Manual\"]", "status": "passed", "duration": "12688ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on Text: \"Done\"", "status": "passed", "duration": "4327ms", "action_id": "7xs3GiydGF", "screenshot_filename": "7xs3GiydGF.png", "report_screenshot": "7xs3GiydGF.png", "resolved_screenshot": "screenshots/7xs3GiydGF.png", "clean_action_id": "7xs3GiydGF", "prefixed_action_id": "al_7xs3GiydGF", "action_id_screenshot": "screenshots/7xs3GiydGF.png"}, {"name": "Restart app: env[appid]", "status": "passed", "duration": "3235ms", "action_id": "t6L5vWfBYM", "screenshot_filename": "t6L5vWfBYM.png", "report_screenshot": "t6L5vWfBYM.png", "resolved_screenshot": "screenshots/t6L5vWfBYM.png", "clean_action_id": "t6L5vWfBYM", "prefixed_action_id": "al_t6L5vWfBYM", "action_id_screenshot": "screenshots/t6L5vWfBYM.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 5 of 5\")]", "status": "passed", "duration": "1871ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on Text: \"Help\"", "status": "passed", "duration": "2758ms", "action_id": "bQrT7FZsxl", "screenshot_filename": "bQrT7FZsxl.png", "report_screenshot": "bQrT7FZsxl.png", "resolved_screenshot": "screenshots/bQrT7FZsxl.png", "clean_action_id": "bQrT7FZsxl", "prefixed_action_id": "al_bQrT7FZsxl", "action_id_screenshot": "screenshots/bQrT7FZsxl.png"}, {"name": "Tap on Text: \"FAQ\"", "status": "passed", "duration": "2557ms", "action_id": "6G6P3UE7Uy", "screenshot_filename": "6G6P3UE7Uy.png", "report_screenshot": "6G6P3UE7Uy.png", "resolved_screenshot": "screenshots/6G6P3UE7Uy.png", "clean_action_id": "6G6P3UE7Uy", "prefixed_action_id": "al_6G6P3UE7Uy", "action_id_screenshot": "screenshots/6G6P3UE7Uy.png"}, {"name": "Swipe from (50%, 70%) to (50%, 30%)", "status": "passed", "duration": "16856ms", "action_id": "YH6erO83XY", "screenshot_filename": "YH6erO83XY.png", "report_screenshot": "YH6erO83XY.png", "resolved_screenshot": "screenshots/YH6erO83XY.png", "clean_action_id": "YH6erO83XY", "prefixed_action_id": "al_YH6erO83XY", "action_id_screenshot": "screenshots/YH6erO83XY.png"}, {"name": "Tap on Text: \"click\"", "status": "passed", "duration": "3229ms", "action_id": "DhFJzlme9K", "screenshot_filename": "DhFJzlme9K.png", "report_screenshot": "DhFJzlme9K.png", "resolved_screenshot": "screenshots/DhFJzlme9K.png", "clean_action_id": "DhFJzlme9K", "prefixed_action_id": "al_DhFJzlme9K", "action_id_screenshot": "screenshots/DhFJzlme9K.png"}, {"name": "Tap on Text: \"1800\"", "status": "passed", "duration": "2821ms", "action_id": "9QADAZGNH3", "screenshot_filename": "9QADAZGNH3.png", "report_screenshot": "9QADAZGNH3.png", "resolved_screenshot": "screenshots/9QADAZGNH3.png", "clean_action_id": "9QADAZGNH3", "prefixed_action_id": "al_9QADAZGNH3", "action_id_screenshot": "screenshots/9QADAZGNH3.png"}, {"name": "Tap on Text: \"+61\"", "status": "passed", "duration": "2487ms", "action_id": "VqSa9z9R2Q", "screenshot_filename": "VqSa9z9R2Q.png", "report_screenshot": "VqSa9z9R2Q.png", "resolved_screenshot": "screenshots/VqSa9z9R2Q.png", "clean_action_id": "VqSa9z9R2Q", "prefixed_action_id": "al_VqSa9z9R2Q", "action_id_screenshot": "screenshots/VqSa9z9R2Q.png"}, {"name": "Launch app: env[appid]", "status": "passed", "duration": "103ms", "action_id": "iDtcdR3nSL", "screenshot_filename": "iDtcdR3nSL.png", "report_screenshot": "iDtcdR3nSL.png", "resolved_screenshot": "screenshots/iDtcdR3nSL.png", "clean_action_id": "iDtcdR3nSL", "prefixed_action_id": "al_iDtcdR3nSL", "action_id_screenshot": "screenshots/iDtcdR3nSL.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 1 of 5\")]", "status": "passed", "duration": "1847ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on Text: \"Find\"", "status": "passed", "duration": "3948ms", "action_id": "MTRbUlaRvI", "screenshot_filename": "MTRbUlaRvI.png", "report_screenshot": "MTRbUlaRvI.png", "resolved_screenshot": "screenshots/MTRbUlaRvI.png", "clean_action_id": "MTRbUlaRvI", "prefixed_action_id": "al_MTRbUlaRvI", "action_id_screenshot": "screenshots/MTRbUlaRvI.png"}, {"name": "iOS Function: text", "status": "passed", "duration": "2157ms", "action_id": "To9Z1FE1lx", "screenshot_filename": "To9Z1FE1lx.png", "report_screenshot": "To9Z1FE1lx.png", "resolved_screenshot": "screenshots/To9Z1FE1lx.png", "clean_action_id": "To9Z1FE1lx", "prefixed_action_id": "al_To9Z1FE1lx", "action_id_screenshot": "screenshots/To9Z1FE1lx.png"}, {"name": "Execute Test Case: Click_Paginations (10 steps)", "status": "passed", "duration": "0ms", "action_id": "Pagination", "screenshot_filename": "Pagination.png", "report_screenshot": "Pagination.png", "resolved_screenshot": "screenshots/Pagination.png"}]}], "passed": 1, "failed": 0, "skipped": 0, "status": "passed"}