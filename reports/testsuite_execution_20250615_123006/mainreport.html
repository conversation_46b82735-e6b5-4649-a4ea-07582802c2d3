<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Suite Report - 6/15/2025, 12:41:49 PM</title>
    <link rel="stylesheet" href="./assets/report.css">
    <link rel="stylesheet" href="./assets/custom.css">
</head>
<body>
    <header class="header">
        <h1>Suites</h1>
        <div class="status-summary">
            Status: <span class="status-badge status-badge-passed">passed</span>
            <span class="stats-summary">
                <span class="passed-count">1</span> passed,
                <span class="failed-count">0</span> failed,
                <span class="skipped-count">0</span> skipped
            </span>
        </div>
    </header>

    <div class="content">
        <div class="suites-panel">
            <div class="suite-heading">
                <span class="expand-icon"></span>
                UI Execution 15/06/2025, 12:41:49
            </div>

            <ul class="test-list">
                <li class="test-item">
                    <div class="test-header">
                        <div class="test-case" data-actions="21 actions">
                            <span class="expand-icon"></span>
                            <span class="status-icon status-icon-passed"></span>
                            #1 Test Case
                        </div>
                        <span class="test-duration">0ms</span>
                    </div>
                    <ul class="test-steps">
                        <li class="test-step" data-step-id="1" data-status="passed"
                            data-screenshot="9Jhn4eWZwR.png" data-action-id="9Jhn4eWZwR" onclick="showStepDetails('step-0-0')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Restart app: env[appid] <span class="action-id-badge" title="Action ID: 9Jhn4eWZwR">9Jhn4eWZwR</span>
                            </div>
                            <span class="test-step-duration">2388ms</span>
                        </li>
                        <li class="test-step" data-step-id="2" data-status="passed"
                            data-screenshot="yNAxs8bgMy.png" data-action-id="yNAxs8bgMy" onclick="showStepDetails('step-0-1')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Tap on Text: "Find" <span class="action-id-badge" title="Action ID: yNAxs8bgMy">yNAxs8bgMy</span>
                            </div>
                            <span class="test-step-duration">4733ms</span>
                        </li>
                        <li class="test-step" data-step-id="3" data-status="passed"
                            data-screenshot="g17Boaefhg.png" data-action-id="g17Boaefhg" onclick="showStepDetails('step-0-2')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                iOS Function: text <span class="action-id-badge" title="Action ID: g17Boaefhg">g17Boaefhg</span>
                            </div>
                            <span class="test-step-duration">2268ms</span>
                        </li>
                        <li class="test-step" data-step-id="4" data-status="passed"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-0-3')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Tap on element with xpath: (//XCUIElementTypeOther[@name="Sort by: Relevance"]/following-sibling::XCUIElementTypeOther[1]//XCUIElementTypeLink)[1] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">1973ms</span>
                        </li>
                        <li class="test-step" data-step-id="5" data-status="passed"
                            data-screenshot="L8LEfGm9WC.png" data-action-id="L8LEfGm9WC" onclick="showStepDetails('step-0-4')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Swipe from (50%, 70%) to (50%, 10%) <span class="action-id-badge" title="Action ID: L8LEfGm9WC">L8LEfGm9WC</span>
                            </div>
                            <span class="test-step-duration">14128ms</span>
                        </li>
                        <li class="test-step" data-step-id="6" data-status="passed"
                            data-screenshot="88BYVcWtJZ.png" data-action-id="88BYVcWtJZ" onclick="showStepDetails('step-0-5')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Swipe from (50%, 80%) to (50%, 10%) <span class="action-id-badge" title="Action ID: 88BYVcWtJZ">88BYVcWtJZ</span>
                            </div>
                            <span class="test-step-duration">15777ms</span>
                        </li>
                        <li class="test-step" data-step-id="7" data-status="passed"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-0-6')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Tap on element with xpath: //XCUIElementTypeStaticText[@name="Instruction Manual"] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">12688ms</span>
                        </li>
                        <li class="test-step" data-step-id="8" data-status="passed"
                            data-screenshot="7xs3GiydGF.png" data-action-id="7xs3GiydGF" onclick="showStepDetails('step-0-7')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Tap on Text: "Done" <span class="action-id-badge" title="Action ID: 7xs3GiydGF">7xs3GiydGF</span>
                            </div>
                            <span class="test-step-duration">4327ms</span>
                        </li>
                        <li class="test-step" data-step-id="9" data-status="passed"
                            data-screenshot="t6L5vWfBYM.png" data-action-id="t6L5vWfBYM" onclick="showStepDetails('step-0-8')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Restart app: env[appid] <span class="action-id-badge" title="Action ID: t6L5vWfBYM">t6L5vWfBYM</span>
                            </div>
                            <span class="test-step-duration">3235ms</span>
                        </li>
                        <li class="test-step" data-step-id="10" data-status="passed"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-0-9')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">1871ms</span>
                        </li>
                        <li class="test-step" data-step-id="11" data-status="passed"
                            data-screenshot="bQrT7FZsxl.png" data-action-id="bQrT7FZsxl" onclick="showStepDetails('step-0-10')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Tap on Text: "Help" <span class="action-id-badge" title="Action ID: bQrT7FZsxl">bQrT7FZsxl</span>
                            </div>
                            <span class="test-step-duration">2758ms</span>
                        </li>
                        <li class="test-step" data-step-id="12" data-status="passed"
                            data-screenshot="6G6P3UE7Uy.png" data-action-id="6G6P3UE7Uy" onclick="showStepDetails('step-0-11')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Tap on Text: "FAQ" <span class="action-id-badge" title="Action ID: 6G6P3UE7Uy">6G6P3UE7Uy</span>
                            </div>
                            <span class="test-step-duration">2557ms</span>
                        </li>
                        <li class="test-step" data-step-id="13" data-status="passed"
                            data-screenshot="YH6erO83XY.png" data-action-id="YH6erO83XY" onclick="showStepDetails('step-0-12')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Swipe from (50%, 70%) to (50%, 30%) <span class="action-id-badge" title="Action ID: YH6erO83XY">YH6erO83XY</span>
                            </div>
                            <span class="test-step-duration">16856ms</span>
                        </li>
                        <li class="test-step" data-step-id="14" data-status="passed"
                            data-screenshot="DhFJzlme9K.png" data-action-id="DhFJzlme9K" onclick="showStepDetails('step-0-13')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Tap on Text: "click" <span class="action-id-badge" title="Action ID: DhFJzlme9K">DhFJzlme9K</span>
                            </div>
                            <span class="test-step-duration">3229ms</span>
                        </li>
                        <li class="test-step" data-step-id="15" data-status="passed"
                            data-screenshot="9QADAZGNH3.png" data-action-id="9QADAZGNH3" onclick="showStepDetails('step-0-14')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Tap on Text: "1800" <span class="action-id-badge" title="Action ID: 9QADAZGNH3">9QADAZGNH3</span>
                            </div>
                            <span class="test-step-duration">2821ms</span>
                        </li>
                        <li class="test-step" data-step-id="16" data-status="passed"
                            data-screenshot="VqSa9z9R2Q.png" data-action-id="VqSa9z9R2Q" onclick="showStepDetails('step-0-15')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Tap on Text: "+61" <span class="action-id-badge" title="Action ID: VqSa9z9R2Q">VqSa9z9R2Q</span>
                            </div>
                            <span class="test-step-duration">2487ms</span>
                        </li>
                        <li class="test-step" data-step-id="17" data-status="passed"
                            data-screenshot="iDtcdR3nSL.png" data-action-id="iDtcdR3nSL" onclick="showStepDetails('step-0-16')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Launch app: env[appid] <span class="action-id-badge" title="Action ID: iDtcdR3nSL">iDtcdR3nSL</span>
                            </div>
                            <span class="test-step-duration">103ms</span>
                        </li>
                        <li class="test-step" data-step-id="18" data-status="passed"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-0-17')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 1 of 5")] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">1847ms</span>
                        </li>
                        <li class="test-step" data-step-id="19" data-status="passed"
                            data-screenshot="MTRbUlaRvI.png" data-action-id="MTRbUlaRvI" onclick="showStepDetails('step-0-18')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Tap on Text: "Find" <span class="action-id-badge" title="Action ID: MTRbUlaRvI">MTRbUlaRvI</span>
                            </div>
                            <span class="test-step-duration">3948ms</span>
                        </li>
                        <li class="test-step" data-step-id="20" data-status="passed"
                            data-screenshot="To9Z1FE1lx.png" data-action-id="To9Z1FE1lx" onclick="showStepDetails('step-0-19')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                iOS Function: text <span class="action-id-badge" title="Action ID: To9Z1FE1lx">To9Z1FE1lx</span>
                            </div>
                            <span class="test-step-duration">2157ms</span>
                        </li>
                        <li class="test-step" data-step-id="21" data-status="passed"
                            data-screenshot="Pagination.png" data-action-id="Pagination" onclick="showStepDetails('step-0-20')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Execute Test Case: Click_Paginations (10 steps) <span class="action-id-badge" title="Action ID: Pagination">Pagination</span>
                            </div>
                            <span class="test-step-duration">0ms</span>
                        </li>
                    </ul>
                </li>
            </ul>
        </div>

        <div class="details-panel" id="details-panel">
            <!-- This will be populated by JavaScript when a test step is clicked -->
            <h3>Click on a test step to view details</h3>
        </div>
    </div>

    <script>
        // Store the test data for our JavaScript to use
        const testData = {"name":"UI Execution 15/06/2025, 12:41:49","testCases":[{"name":"Test Case","status":"passed","steps":[{"name":"Restart app: env[appid]","status":"passed","duration":"2388ms","action_id":"9Jhn4eWZwR","screenshot_filename":"9Jhn4eWZwR.png","report_screenshot":"9Jhn4eWZwR.png","resolved_screenshot":"screenshots/9Jhn4eWZwR.png","clean_action_id":"9Jhn4eWZwR","prefixed_action_id":"al_9Jhn4eWZwR","action_id_screenshot":"screenshots/9Jhn4eWZwR.png"},{"name":"Tap on Text: \"Find\"","status":"passed","duration":"4733ms","action_id":"yNAxs8bgMy","screenshot_filename":"yNAxs8bgMy.png","report_screenshot":"yNAxs8bgMy.png","resolved_screenshot":"screenshots/yNAxs8bgMy.png","clean_action_id":"yNAxs8bgMy","prefixed_action_id":"al_yNAxs8bgMy","action_id_screenshot":"screenshots/yNAxs8bgMy.png"},{"name":"iOS Function: text","status":"passed","duration":"2268ms","action_id":"g17Boaefhg","screenshot_filename":"g17Boaefhg.png","report_screenshot":"g17Boaefhg.png","resolved_screenshot":"screenshots/g17Boaefhg.png","clean_action_id":"g17Boaefhg","prefixed_action_id":"al_g17Boaefhg","action_id_screenshot":"screenshots/g17Boaefhg.png"},{"name":"Tap on element with xpath: (//XCUIElementTypeOther[@name=\"Sort by: Relevance\"]/following-sibling::XCUIElementTypeOther[1]//XCUIElementTypeLink)[1]","status":"passed","duration":"1973ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Swipe from (50%, 70%) to (50%, 10%)","status":"passed","duration":"14128ms","action_id":"L8LEfGm9WC","screenshot_filename":"L8LEfGm9WC.png","report_screenshot":"L8LEfGm9WC.png","resolved_screenshot":"screenshots/L8LEfGm9WC.png","clean_action_id":"L8LEfGm9WC","prefixed_action_id":"al_L8LEfGm9WC","action_id_screenshot":"screenshots/L8LEfGm9WC.png"},{"name":"Swipe from (50%, 80%) to (50%, 10%)","status":"passed","duration":"15777ms","action_id":"88BYVcWtJZ","screenshot_filename":"88BYVcWtJZ.png","report_screenshot":"88BYVcWtJZ.png","resolved_screenshot":"screenshots/88BYVcWtJZ.png","clean_action_id":"88BYVcWtJZ","prefixed_action_id":"al_88BYVcWtJZ","action_id_screenshot":"screenshots/88BYVcWtJZ.png"},{"name":"Tap on element with xpath: //XCUIElementTypeStaticText[@name=\"Instruction Manual\"]","status":"passed","duration":"12688ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Tap on Text: \"Done\"","status":"passed","duration":"4327ms","action_id":"7xs3GiydGF","screenshot_filename":"7xs3GiydGF.png","report_screenshot":"7xs3GiydGF.png","resolved_screenshot":"screenshots/7xs3GiydGF.png","clean_action_id":"7xs3GiydGF","prefixed_action_id":"al_7xs3GiydGF","action_id_screenshot":"screenshots/7xs3GiydGF.png"},{"name":"Restart app: env[appid]","status":"passed","duration":"3235ms","action_id":"t6L5vWfBYM","screenshot_filename":"t6L5vWfBYM.png","report_screenshot":"t6L5vWfBYM.png","resolved_screenshot":"screenshots/t6L5vWfBYM.png","clean_action_id":"t6L5vWfBYM","prefixed_action_id":"al_t6L5vWfBYM","action_id_screenshot":"screenshots/t6L5vWfBYM.png"},{"name":"Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 5 of 5\")]","status":"passed","duration":"1871ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Tap on Text: \"Help\"","status":"passed","duration":"2758ms","action_id":"bQrT7FZsxl","screenshot_filename":"bQrT7FZsxl.png","report_screenshot":"bQrT7FZsxl.png","resolved_screenshot":"screenshots/bQrT7FZsxl.png","clean_action_id":"bQrT7FZsxl","prefixed_action_id":"al_bQrT7FZsxl","action_id_screenshot":"screenshots/bQrT7FZsxl.png"},{"name":"Tap on Text: \"FAQ\"","status":"passed","duration":"2557ms","action_id":"6G6P3UE7Uy","screenshot_filename":"6G6P3UE7Uy.png","report_screenshot":"6G6P3UE7Uy.png","resolved_screenshot":"screenshots/6G6P3UE7Uy.png","clean_action_id":"6G6P3UE7Uy","prefixed_action_id":"al_6G6P3UE7Uy","action_id_screenshot":"screenshots/6G6P3UE7Uy.png"},{"name":"Swipe from (50%, 70%) to (50%, 30%)","status":"passed","duration":"16856ms","action_id":"YH6erO83XY","screenshot_filename":"YH6erO83XY.png","report_screenshot":"YH6erO83XY.png","resolved_screenshot":"screenshots/YH6erO83XY.png","clean_action_id":"YH6erO83XY","prefixed_action_id":"al_YH6erO83XY","action_id_screenshot":"screenshots/YH6erO83XY.png"},{"name":"Tap on Text: \"click\"","status":"passed","duration":"3229ms","action_id":"DhFJzlme9K","screenshot_filename":"DhFJzlme9K.png","report_screenshot":"DhFJzlme9K.png","resolved_screenshot":"screenshots/DhFJzlme9K.png","clean_action_id":"DhFJzlme9K","prefixed_action_id":"al_DhFJzlme9K","action_id_screenshot":"screenshots/DhFJzlme9K.png"},{"name":"Tap on Text: \"1800\"","status":"passed","duration":"2821ms","action_id":"9QADAZGNH3","screenshot_filename":"9QADAZGNH3.png","report_screenshot":"9QADAZGNH3.png","resolved_screenshot":"screenshots/9QADAZGNH3.png","clean_action_id":"9QADAZGNH3","prefixed_action_id":"al_9QADAZGNH3","action_id_screenshot":"screenshots/9QADAZGNH3.png"},{"name":"Tap on Text: \"+61\"","status":"passed","duration":"2487ms","action_id":"VqSa9z9R2Q","screenshot_filename":"VqSa9z9R2Q.png","report_screenshot":"VqSa9z9R2Q.png","resolved_screenshot":"screenshots/VqSa9z9R2Q.png","clean_action_id":"VqSa9z9R2Q","prefixed_action_id":"al_VqSa9z9R2Q","action_id_screenshot":"screenshots/VqSa9z9R2Q.png"},{"name":"Launch app: env[appid]","status":"passed","duration":"103ms","action_id":"iDtcdR3nSL","screenshot_filename":"iDtcdR3nSL.png","report_screenshot":"iDtcdR3nSL.png","resolved_screenshot":"screenshots/iDtcdR3nSL.png","clean_action_id":"iDtcdR3nSL","prefixed_action_id":"al_iDtcdR3nSL","action_id_screenshot":"screenshots/iDtcdR3nSL.png"},{"name":"Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 1 of 5\")]","status":"passed","duration":"1847ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Tap on Text: \"Find\"","status":"passed","duration":"3948ms","action_id":"MTRbUlaRvI","screenshot_filename":"MTRbUlaRvI.png","report_screenshot":"MTRbUlaRvI.png","resolved_screenshot":"screenshots/MTRbUlaRvI.png","clean_action_id":"MTRbUlaRvI","prefixed_action_id":"al_MTRbUlaRvI","action_id_screenshot":"screenshots/MTRbUlaRvI.png"},{"name":"iOS Function: text","status":"passed","duration":"2157ms","action_id":"To9Z1FE1lx","screenshot_filename":"To9Z1FE1lx.png","report_screenshot":"To9Z1FE1lx.png","resolved_screenshot":"screenshots/To9Z1FE1lx.png","clean_action_id":"To9Z1FE1lx","prefixed_action_id":"al_To9Z1FE1lx","action_id_screenshot":"screenshots/To9Z1FE1lx.png"},{"name":"Execute Test Case: Click_Paginations (10 steps)","status":"passed","duration":"0ms","action_id":"Pagination","screenshot_filename":"Pagination.png","report_screenshot":"Pagination.png","resolved_screenshot":"screenshots/Pagination.png","action_id_screenshot":"screenshots/Pagination.png"}]}],"passed":1,"failed":0,"skipped":0,"status":"passed","availableScreenshots":["6G6P3UE7Uy.png","7xs3GiydGF.png","88BYVcWtJZ.png","9Jhn4eWZwR.png","9QADAZGNH3.png","DhFJzlme9K.png","I0tM87Yjhc.png","IL6kON0uQ9.png","L8LEfGm9WC.png","MTRbUlaRvI.png","OR0SKKnFxy.png","RHEU77LRMw.png","SqDiBhmyOG.png","To9Z1FE1lx.png","VqSa9z9R2Q.png","Vy3WZ0LTJF.png","YH6erO83XY.png","bQrT7FZsxl.png","g17Boaefhg.png","iDtcdR3nSL.png","t6L5vWfBYM.png","yNAxs8bgMy.png"],"screenshots_map":{}};
    </script>

    <script src="./assets/report.js"></script>
</body>
</html>