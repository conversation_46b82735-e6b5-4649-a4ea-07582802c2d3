Action Log - 2025-06-15 12:41:49
================================================================================

[[12:41:49]] [INFO] Generating execution report...
[[12:41:49]] [SUCCESS] All tests passed successfully!
[[12:41:49]] [SUCCESS] Screenshot refreshed
[[12:41:49]] [INFO] Refreshing screenshot...
[[12:41:48]] [SUCCESS] Screenshot refreshed
[[12:41:48]] [INFO] Refreshing screenshot...
[[12:41:43]] [SUCCESS] Screenshot refreshed successfully
[[12:41:43]] [SUCCESS] Screenshot refreshed successfully
[[12:41:43]] [INFO] Executing Multi Step action step 10/10: Tap on element with xpath: //XCUIElementTypeButton[@name="Go to previous page"]
[[12:41:42]] [SUCCESS] Screenshot refreshed
[[12:41:42]] [INFO] Refreshing screenshot...
[[12:39:59]] [SUCCESS] Screenshot refreshed successfully
[[12:39:59]] [SUCCESS] Screenshot refreshed successfully
[[12:39:58]] [INFO] Executing Multi Step action step 9/10: Swipe from (50%, 80%) to (50%, 10%)
[[12:39:58]] [SUCCESS] Screenshot refreshed
[[12:39:58]] [INFO] Refreshing screenshot...
[[12:39:52]] [SUCCESS] Screenshot refreshed successfully
[[12:39:52]] [SUCCESS] Screenshot refreshed successfully
[[12:39:52]] [INFO] Executing Multi Step action step 8/10: Tap on element with xpath: //XCUIElementTypeButton[@name="Go to previous page"]
[[12:39:51]] [SUCCESS] Screenshot refreshed
[[12:39:51]] [INFO] Refreshing screenshot...
[[12:38:09]] [SUCCESS] Screenshot refreshed successfully
[[12:38:09]] [SUCCESS] Screenshot refreshed successfully
[[12:38:09]] [INFO] Executing Multi Step action step 7/10: Swipe from (50%, 80%) to (50%, 10%)
[[12:38:08]] [SUCCESS] Screenshot refreshed
[[12:38:08]] [INFO] Refreshing screenshot...
[[12:38:03]] [SUCCESS] Screenshot refreshed successfully
[[12:38:03]] [SUCCESS] Screenshot refreshed successfully
[[12:38:02]] [INFO] Executing Multi Step action step 6/10: Tap on element with xpath: //XCUIElementTypeButton[@name="Go to next page"]
[[12:38:02]] [SUCCESS] Screenshot refreshed
[[12:38:02]] [INFO] Refreshing screenshot...
[[12:36:19]] [SUCCESS] Screenshot refreshed successfully
[[12:36:19]] [SUCCESS] Screenshot refreshed successfully
[[12:36:18]] [INFO] Executing Multi Step action step 5/10: Swipe from (50%, 80%) to (50%, 10%)
[[12:36:17]] [SUCCESS] Screenshot refreshed
[[12:36:17]] [INFO] Refreshing screenshot...
[[12:36:12]] [SUCCESS] Screenshot refreshed successfully
[[12:36:12]] [SUCCESS] Screenshot refreshed successfully
[[12:36:11]] [INFO] Executing Multi Step action step 4/10: Tap on element with xpath: //XCUIElementTypeButton[@name="Go to next page"]
[[12:36:11]] [SUCCESS] Screenshot refreshed
[[12:36:11]] [INFO] Refreshing screenshot...
[[12:34:27]] [SUCCESS] Screenshot refreshed successfully
[[12:34:27]] [SUCCESS] Screenshot refreshed successfully
[[12:34:26]] [INFO] Executing Multi Step action step 3/10: Swipe from (50%, 80%) to (50%, 10%)
[[12:34:26]] [SUCCESS] Screenshot refreshed
[[12:34:26]] [INFO] Refreshing screenshot...
[[12:34:20]] [SUCCESS] Screenshot refreshed successfully
[[12:34:20]] [SUCCESS] Screenshot refreshed successfully
[[12:34:20]] [INFO] Executing Multi Step action step 2/10: Tap on element with xpath: //XCUIElementTypeButton[@name="Go to next page"]
[[12:34:19]] [SUCCESS] Screenshot refreshed
[[12:34:19]] [INFO] Refreshing screenshot...
[[12:32:34]] [SUCCESS] Screenshot refreshed successfully
[[12:32:34]] [SUCCESS] Screenshot refreshed successfully
[[12:32:34]] [INFO] Executing Multi Step action step 1/10: Swipe from (50%, 80%) to (50%, 10%)
[[12:32:34]] [INFO] Loaded 10 steps from test case: Click_Paginations
[[12:32:34]] [INFO] Loading steps for Multi Step action: Click_Paginations
[[12:32:34]] [INFO] Executing action 21/21: Execute Test Case: Click_Paginations (10 steps)
[[12:32:33]] [SUCCESS] Screenshot refreshed
[[12:32:33]] [INFO] Refreshing screenshot...
[[12:32:29]] [SUCCESS] Screenshot refreshed successfully
[[12:32:29]] [SUCCESS] Screenshot refreshed successfully
[[12:32:29]] [INFO] Executing action 20/21: iOS Function: text
[[12:32:29]] [SUCCESS] Screenshot refreshed
[[12:32:29]] [INFO] Refreshing screenshot...
[[12:32:24]] [SUCCESS] Screenshot refreshed successfully
[[12:32:24]] [SUCCESS] Screenshot refreshed successfully
[[12:32:23]] [INFO] Executing action 19/21: Tap on Text: "Find"
[[12:32:22]] [SUCCESS] Screenshot refreshed
[[12:32:22]] [INFO] Refreshing screenshot...
[[12:32:19]] [SUCCESS] Screenshot refreshed successfully
[[12:32:19]] [SUCCESS] Screenshot refreshed successfully
[[12:32:19]] [INFO] Executing action 18/21: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 1 of 5")]
[[12:32:18]] [SUCCESS] Screenshot refreshed
[[12:32:18]] [INFO] Refreshing screenshot...
[[12:32:16]] [INFO] Executing action 17/21: Launch app: env[appid]
[[12:32:16]] [SUCCESS] Screenshot refreshed successfully
[[12:32:16]] [SUCCESS] Screenshot refreshed successfully
[[12:32:16]] [SUCCESS] Screenshot refreshed
[[12:32:16]] [INFO] Refreshing screenshot...
[[12:32:12]] [SUCCESS] Screenshot refreshed successfully
[[12:32:12]] [SUCCESS] Screenshot refreshed successfully
[[12:32:12]] [INFO] Executing action 16/21: Tap on Text: "+61"
[[12:32:11]] [SUCCESS] Screenshot refreshed
[[12:32:11]] [INFO] Refreshing screenshot...
[[12:32:07]] [SUCCESS] Screenshot refreshed successfully
[[12:32:07]] [SUCCESS] Screenshot refreshed successfully
[[12:32:07]] [INFO] Executing action 15/21: Tap on Text: "1800"
[[12:32:07]] [SUCCESS] Screenshot refreshed
[[12:32:07]] [INFO] Refreshing screenshot...
[[12:32:02]] [SUCCESS] Screenshot refreshed successfully
[[12:32:02]] [SUCCESS] Screenshot refreshed successfully
[[12:32:02]] [INFO] Executing action 14/21: Tap on Text: "click"
[[12:32:01]] [SUCCESS] Screenshot refreshed
[[12:32:01]] [INFO] Refreshing screenshot...
[[12:31:43]] [SUCCESS] Screenshot refreshed successfully
[[12:31:43]] [SUCCESS] Screenshot refreshed successfully
[[12:31:43]] [INFO] Executing action 13/21: Swipe from (50%, 70%) to (50%, 30%)
[[12:31:42]] [SUCCESS] Screenshot refreshed
[[12:31:42]] [INFO] Refreshing screenshot...
[[12:31:38]] [SUCCESS] Screenshot refreshed successfully
[[12:31:38]] [SUCCESS] Screenshot refreshed successfully
[[12:31:38]] [INFO] Executing action 12/21: Tap on Text: "FAQ"
[[12:31:37]] [SUCCESS] Screenshot refreshed
[[12:31:37]] [INFO] Refreshing screenshot...
[[12:31:33]] [SUCCESS] Screenshot refreshed successfully
[[12:31:33]] [SUCCESS] Screenshot refreshed successfully
[[12:31:33]] [INFO] Executing action 11/21: Tap on Text: "Help"
[[12:31:33]] [SUCCESS] Screenshot refreshed
[[12:31:33]] [INFO] Refreshing screenshot...
[[12:31:30]] [SUCCESS] Screenshot refreshed successfully
[[12:31:30]] [SUCCESS] Screenshot refreshed successfully
[[12:31:29]] [INFO] Executing action 10/21: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[12:31:29]] [SUCCESS] Screenshot refreshed
[[12:31:29]] [INFO] Refreshing screenshot...
[[12:31:24]] [SUCCESS] Screenshot refreshed successfully
[[12:31:24]] [SUCCESS] Screenshot refreshed successfully
[[12:31:23]] [INFO] Executing action 9/21: Restart app: env[appid]
[[12:31:23]] [SUCCESS] Screenshot refreshed
[[12:31:23]] [INFO] Refreshing screenshot...
[[12:31:17]] [SUCCESS] Screenshot refreshed successfully
[[12:31:17]] [SUCCESS] Screenshot refreshed successfully
[[12:31:17]] [INFO] Executing action 8/21: Tap on Text: "Done"
[[12:31:17]] [SUCCESS] Screenshot refreshed
[[12:31:17]] [INFO] Refreshing screenshot...
[[12:31:02]] [SUCCESS] Screenshot refreshed successfully
[[12:31:02]] [SUCCESS] Screenshot refreshed successfully
[[12:31:02]] [INFO] Executing action 7/21: Tap on element with xpath: //XCUIElementTypeStaticText[@name="Instruction Manual"]
[[12:31:02]] [SUCCESS] Screenshot refreshed
[[12:31:02]] [INFO] Refreshing screenshot...
[[12:30:45]] [SUCCESS] Screenshot refreshed successfully
[[12:30:45]] [SUCCESS] Screenshot refreshed successfully
[[12:30:44]] [INFO] Executing action 6/21: Swipe from (50%, 80%) to (50%, 10%)
[[12:30:44]] [SUCCESS] Screenshot refreshed
[[12:30:44]] [INFO] Refreshing screenshot...
[[12:30:29]] [SUCCESS] Screenshot refreshed successfully
[[12:30:29]] [SUCCESS] Screenshot refreshed successfully
[[12:30:28]] [INFO] Executing action 5/21: Swipe from (50%, 70%) to (50%, 10%)
[[12:30:27]] [SUCCESS] Screenshot refreshed
[[12:30:27]] [INFO] Refreshing screenshot...
[[12:30:24]] [SUCCESS] Screenshot refreshed successfully
[[12:30:24]] [SUCCESS] Screenshot refreshed successfully
[[12:30:23]] [INFO] Executing action 4/21: Tap on element with xpath: (//XCUIElementTypeOther[@name="Sort by: Relevance"]/following-sibling::XCUIElementTypeOther[1]//XCUIElementTypeLink)[1]
[[12:30:23]] [SUCCESS] Screenshot refreshed
[[12:30:23]] [INFO] Refreshing screenshot...
[[12:30:19]] [SUCCESS] Screenshot refreshed successfully
[[12:30:19]] [SUCCESS] Screenshot refreshed successfully
[[12:30:19]] [INFO] Executing action 3/21: iOS Function: text
[[12:30:18]] [SUCCESS] Screenshot refreshed
[[12:30:18]] [INFO] Refreshing screenshot...
[[12:30:12]] [SUCCESS] Screenshot refreshed successfully
[[12:30:12]] [SUCCESS] Screenshot refreshed successfully
[[12:30:12]] [INFO] Executing action 2/21: Tap on Text: "Find"
[[12:30:11]] [SUCCESS] Screenshot refreshed
[[12:30:11]] [INFO] Refreshing screenshot...
[[12:30:06]] [INFO] Executing action 1/21: Restart app: env[appid]
[[12:30:06]] [INFO] ExecutionManager: Starting execution of 21 actions...
[[12:30:06]] [WARNING] Failed to clear screenshots: Error clearing screenshots: No module named 'app.utils'; 'app' is not a package
[[12:30:06]] [INFO] Clearing screenshots from database before execution...
[[12:30:06]] [SUCCESS] All screenshots deleted successfully
[[12:30:06]] [INFO] Deleting all screenshots in app/static/screenshots folder...
[[12:30:06]] [INFO] Screenshots directory: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/reports/testsuite_execution_20250615_123006/screenshots
[[12:30:06]] [INFO] Report directory: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/reports/testsuite_execution_20250615_123006
[[12:30:06]] [SUCCESS] Report directory initialized successfully
[[12:30:06]] [INFO] Initializing report directory and screenshots folder...
[[12:30:00]] [SUCCESS] Test case temp saved successfully
[[12:30:00]] [INFO] Saving test case "temp"...
[[12:29:58]] [INFO] Action removed at index 20
[[12:29:55]] [SUCCESS] Added multiStep action
[[12:29:55]] [SUCCESS] Added action: multiStep
[[12:29:43]] [SUCCESS] All screenshots deleted successfully
[[12:29:43]] [SUCCESS] Loaded test case "temp" with 21 actions
[[12:29:43]] [SUCCESS] Added action: multiStep
[[12:29:43]] [SUCCESS] Added action: iosFunctions
[[12:29:43]] [SUCCESS] Added action: tapOnText
[[12:29:43]] [SUCCESS] Added action: tap
[[12:29:43]] [SUCCESS] Added action: launchApp
[[12:29:43]] [SUCCESS] Added action: tapOnText
[[12:29:43]] [SUCCESS] Added action: tapOnText
[[12:29:43]] [SUCCESS] Added action: tapOnText
[[12:29:43]] [SUCCESS] Added action: swipe
[[12:29:43]] [SUCCESS] Added action: tapOnText
[[12:29:43]] [SUCCESS] Added action: tapOnText
[[12:29:43]] [SUCCESS] Added action: tap
[[12:29:43]] [SUCCESS] Added action: restartApp
[[12:29:43]] [SUCCESS] Added action: tapOnText
[[12:29:43]] [SUCCESS] Added action: tap
[[12:29:43]] [SUCCESS] Added action: swipe
[[12:29:43]] [SUCCESS] Added action: swipe
[[12:29:43]] [SUCCESS] Added action: tap
[[12:29:43]] [SUCCESS] Added action: iosFunctions
[[12:29:43]] [SUCCESS] Added action: tapOnText
[[12:29:43]] [SUCCESS] Added action: restartApp
[[12:29:43]] [INFO] All actions cleared
[[12:29:43]] [INFO] Cleaning up screenshots...
[[12:29:39]] [SUCCESS] Test case Click_Paginations saved successfully
[[12:29:39]] [INFO] Saving test case "Click_Paginations"...
[[12:29:37]] [INFO] Action removed at index 9
[[12:29:31]] [SUCCESS] Duplicated action at index 7
[[12:29:31]] [SUCCESS] Added action at position 8
[[12:29:28]] [INFO] Action removed at index 7
[[12:29:22]] [SUCCESS] Duplicated action at index 5
[[12:29:22]] [SUCCESS] Added action at position 6
[[12:29:19]] [INFO] Action removed at index 5
[[12:29:13]] [SUCCESS] Duplicated action at index 3
[[12:29:13]] [SUCCESS] Added action at position 4
[[12:29:11]] [INFO] Action removed at index 3
[[12:29:05]] [SUCCESS] Duplicated action at index 1
[[12:29:05]] [SUCCESS] Added action at position 2
[[12:29:01]] [SUCCESS] Updated action #1
[[12:28:52]] [SUCCESS] All screenshots deleted successfully
[[12:28:52]] [SUCCESS] Loaded test case "Click_Paginations" with 10 actions
[[12:28:52]] [SUCCESS] Added action: tap
[[12:28:52]] [SUCCESS] Added action: swipe
[[12:28:52]] [SUCCESS] Added action: tap
[[12:28:52]] [SUCCESS] Added action: swipe
[[12:28:52]] [SUCCESS] Added action: tap
[[12:28:52]] [SUCCESS] Added action: swipe
[[12:28:52]] [SUCCESS] Added action: tap
[[12:28:52]] [SUCCESS] Added action: swipe
[[12:28:52]] [SUCCESS] Added action: tap
[[12:28:52]] [SUCCESS] Added action: swipe
[[12:28:52]] [INFO] All actions cleared
[[12:28:52]] [INFO] Cleaning up screenshots...
[[12:28:45]] [SUCCESS] Test case temp saved successfully
[[12:28:45]] [INFO] Saving test case "temp"...
[[12:28:42]] [SUCCESS] Updated action #6
[[12:28:25]] [SUCCESS] Updated action #5
[[12:28:16]] [SUCCESS] All screenshots deleted successfully
[[12:28:16]] [SUCCESS] Loaded test case "temp" with 21 actions
[[12:28:16]] [SUCCESS] Added action: multiStep
[[12:28:16]] [SUCCESS] Added action: iosFunctions
[[12:28:16]] [SUCCESS] Added action: tapOnText
[[12:28:16]] [SUCCESS] Added action: tap
[[12:28:16]] [SUCCESS] Added action: launchApp
[[12:28:16]] [SUCCESS] Added action: tapOnText
[[12:28:16]] [SUCCESS] Added action: tapOnText
[[12:28:16]] [SUCCESS] Added action: tapOnText
[[12:28:16]] [SUCCESS] Added action: swipe
[[12:28:16]] [SUCCESS] Added action: tapOnText
[[12:28:16]] [SUCCESS] Added action: tapOnText
[[12:28:16]] [SUCCESS] Added action: tap
[[12:28:16]] [SUCCESS] Added action: restartApp
[[12:28:16]] [SUCCESS] Added action: tapOnText
[[12:28:16]] [SUCCESS] Added action: tap
[[12:28:16]] [SUCCESS] Added action: swipe
[[12:28:16]] [SUCCESS] Added action: swipe
[[12:28:16]] [SUCCESS] Added action: tap
[[12:28:16]] [SUCCESS] Added action: iosFunctions
[[12:28:16]] [SUCCESS] Added action: tapOnText
[[12:28:16]] [SUCCESS] Added action: restartApp
[[12:28:16]] [INFO] All actions cleared
[[12:28:16]] [INFO] Cleaning up screenshots...
[[12:28:13]] [SUCCESS] Screenshot refreshed successfully
[[12:28:12]] [SUCCESS] Screenshot refreshed
[[12:28:12]] [INFO] Refreshing screenshot...
[[12:28:11]] [SUCCESS] Connected to device: 00008120-00186C801E13C01E with AirTest support
[[12:28:11]] [INFO] Device info updated: 00008120-00186C801E13C01E
[[12:28:07]] [INFO] Connecting to device: 00008120-00186C801E13C01E (Platform: iOS)...
[[12:28:05]] [SUCCESS] Found 1 device(s)
[[12:28:04]] [INFO] Refreshing device list...
