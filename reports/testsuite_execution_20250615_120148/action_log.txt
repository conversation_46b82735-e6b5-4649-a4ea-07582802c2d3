Action Log - 2025-06-15 12:02:40
================================================================================

[[12:02:40]] [INFO] Generating execution report...
[[12:02:40]] [SUCCESS] All tests passed successfully!
[[12:02:40]] [WARNING] Execution stopped by user.
[[12:02:40]] [ERROR] Error executing action 7: Failed to fetch
[[12:02:35]] [WARNING] Stop requested. Finishing current action...
[[12:02:30]] [SUCCESS] Screenshot refreshed successfully
[[12:02:30]] [SUCCESS] Screenshot refreshed successfully
[[12:02:29]] [INFO] Executing action 7/21: Tap on element with xpath: //XCUIElementTypeStaticText[@name="Instruction Manual"]
[[12:02:29]] [SUCCESS] Screenshot refreshed
[[12:02:29]] [INFO] Refreshing screenshot...
[[12:02:20]] [SUCCESS] Screenshot refreshed successfully
[[12:02:20]] [SUCCESS] Screenshot refreshed successfully
[[12:02:20]] [INFO] Executing action 6/21: Swipe from (50%, 80%) to (50%, 10%)
[[12:02:19]] [SUCCESS] Screenshot refreshed
[[12:02:19]] [INFO] Refreshing screenshot...
[[12:02:11]] [SUCCESS] Screenshot refreshed successfully
[[12:02:11]] [SUCCESS] Screenshot refreshed successfully
[[12:02:10]] [INFO] Executing action 5/21: Swipe from (50%, 70%) to (50%, 10%)
[[12:02:10]] [SUCCESS] Screenshot refreshed
[[12:02:10]] [INFO] Refreshing screenshot...
[[12:02:06]] [SUCCESS] Screenshot refreshed successfully
[[12:02:06]] [SUCCESS] Screenshot refreshed successfully
[[12:02:05]] [INFO] Executing action 4/21: Tap on element with xpath: (//XCUIElementTypeOther[@name="Sort by: Relevance"]/following-sibling::XCUIElementTypeOther[1]//XCUIElementTypeLink)[1]
[[12:02:05]] [SUCCESS] Screenshot refreshed
[[12:02:05]] [INFO] Refreshing screenshot...
[[12:02:01]] [SUCCESS] Screenshot refreshed successfully
[[12:02:01]] [SUCCESS] Screenshot refreshed successfully
[[12:02:01]] [INFO] Executing action 3/21: iOS Function: text
[[12:02:00]] [SUCCESS] Screenshot refreshed
[[12:02:00]] [INFO] Refreshing screenshot...
[[12:01:54]] [SUCCESS] Screenshot refreshed successfully
[[12:01:54]] [SUCCESS] Screenshot refreshed successfully
[[12:01:54]] [INFO] Executing action 2/21: Tap on Text: "Find"
[[12:01:53]] [SUCCESS] Screenshot refreshed
[[12:01:53]] [INFO] Refreshing screenshot...
[[12:01:48]] [INFO] Executing action 1/21: Restart app: env[appid]
[[12:01:48]] [INFO] ExecutionManager: Starting execution of 21 actions...
[[12:01:48]] [WARNING] Failed to clear screenshots: Error clearing screenshots: No module named 'app.utils'; 'app' is not a package
[[12:01:48]] [INFO] Clearing screenshots from database before execution...
[[12:01:48]] [SUCCESS] All screenshots deleted successfully
[[12:01:48]] [INFO] Deleting all screenshots in app/static/screenshots folder...
[[12:01:48]] [INFO] Screenshots directory: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/reports/testsuite_execution_20250615_120148/screenshots
[[12:01:48]] [INFO] Report directory: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/reports/testsuite_execution_20250615_120148
[[12:01:48]] [SUCCESS] Report directory initialized successfully
[[12:01:48]] [INFO] Initializing report directory and screenshots folder...
[[12:01:45]] [SUCCESS] All screenshots deleted successfully
[[12:01:45]] [SUCCESS] Loaded test case "temp" with 21 actions
[[12:01:45]] [SUCCESS] Added action: multiStep
[[12:01:45]] [SUCCESS] Added action: iosFunctions
[[12:01:45]] [SUCCESS] Added action: tapOnText
[[12:01:45]] [SUCCESS] Added action: tap
[[12:01:45]] [SUCCESS] Added action: launchApp
[[12:01:45]] [SUCCESS] Added action: tapOnText
[[12:01:45]] [SUCCESS] Added action: tapOnText
[[12:01:45]] [SUCCESS] Added action: tapOnText
[[12:01:45]] [SUCCESS] Added action: swipe
[[12:01:45]] [SUCCESS] Added action: tapOnText
[[12:01:45]] [SUCCESS] Added action: tapOnText
[[12:01:45]] [SUCCESS] Added action: tap
[[12:01:45]] [SUCCESS] Added action: restartApp
[[12:01:45]] [SUCCESS] Added action: tapOnText
[[12:01:45]] [SUCCESS] Added action: tap
[[12:01:45]] [SUCCESS] Added action: swipe
[[12:01:45]] [SUCCESS] Added action: swipe
[[12:01:45]] [SUCCESS] Added action: tap
[[12:01:45]] [SUCCESS] Added action: iosFunctions
[[12:01:45]] [SUCCESS] Added action: tapOnText
[[12:01:45]] [SUCCESS] Added action: restartApp
[[12:01:45]] [INFO] All actions cleared
[[12:01:45]] [INFO] Cleaning up screenshots...
[[12:01:44]] [SUCCESS] Screenshot refreshed successfully
[[12:01:43]] [SUCCESS] Screenshot refreshed
[[12:01:43]] [INFO] Refreshing screenshot...
[[12:01:42]] [SUCCESS] Connected to device: 00008120-00186C801E13C01E with AirTest support
[[12:01:42]] [INFO] Device info updated: 00008120-00186C801E13C01E
[[12:01:39]] [INFO] Connecting to device: 00008120-00186C801E13C01E (Platform: iOS)...
[[12:01:36]] [SUCCESS] Found 1 device(s)
[[12:01:35]] [INFO] Refreshing device list...
